import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../../../shared/types';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  err: AppError,
  _req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const { statusCode = 500, message } = err;

  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    statusCode
  });

  const response: ApiResponse<null> = {
    success: false,
    message: process.env.NODE_ENV === 'production' 
      ? (statusCode === 500 ? '服务器内部错误' : message)
      : message
  };

  res.status(statusCode).json(response);
};

export const notFoundHandler = (_req: Request, res: Response): void => {
  const response: ApiResponse<null> = {
    success: false,
    message: '请求的资源不存在'
  };
  
  res.status(404).json(response);
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
