import 'dotenv/config';

export const config = {
  // 服务器配置
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // 安全配置
  adminPassword: process.env.ADMIN_PASSWORD || 'admin123',
  jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
  sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
  
  // 数据库配置
  databasePath: process.env.DATABASE_PATH || './subscriptions.db',
  
  // CORS配置
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  
  // API配置
  apiTimeout: parseInt(process.env.API_TIMEOUT || '10000'),
  
  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'info',

  // 智能记录配置
  recordInterval: parseInt(process.env.RECORD_INTERVAL || '60'), // 默认60分钟

  // 开发模式检查
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
} as const;

// 验证必要的环境变量
export const validateConfig = (): void => {
  const requiredVars = ['ADMIN_PASSWORD'];
  
  if (config.isProduction) {
    requiredVars.push('JWT_SECRET', 'SESSION_SECRET');
  }
  
  const missing = requiredVars.filter(varName => {
    const value = process.env[varName];
    return !value || (varName.includes('SECRET') && value.length < 32);
  });
  
  if (missing.length > 0) {
    console.warn('警告: 以下环境变量未设置或不安全:', missing.join(', '));
    if (config.isProduction) {
      throw new Error('生产环境必须设置所有必要的环境变量');
    }
  }
};
