@echo off
echo 🧪 测试登录功能...
echo.

echo 检查后端是否运行...
curl -s http://localhost:3001/api/subscriptions >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 后端服务未运行，请先启动后端服务
    echo 运行命令: npm run dev:backend
    pause
    exit /b 1
)

echo ✅ 后端服务正在运行

echo.
echo 测试登录API...
curl -X POST -H "Content-Type: application/json" -d "{\"password\":\"admin123\"}" http://localhost:3001/api/admin/login

echo.
echo.
echo 📝 如果看到 {"success":true,"data":{"success":true},"message":"登录成功"}
echo    说明登录API工作正常
echo.
echo 📝 如果看到 {"success":false,"message":"密码错误"}
echo    说明密码不正确，请检查环境变量配置
echo.
pause
