# 部署指南

## 开发环境部署

### 1. 环境准备
- Node.js >= 16.0.0
- npm >= 8.0.0

### 2. 快速开始
```bash
# 运行安装脚本
setup.bat

# 启动开发服务器
start.bat
```

### 3. 手动安装
```bash
# 1. 安装依赖
npm run install:all

# 2. 配置环境变量
cp .env.example .env
cp backend/.env.example backend/.env

# 3. 启动开发服务器
npm run dev
```

## 生产环境部署

### 1. 构建项目
```bash
# 测试构建
test-build.bat

# 或手动构建
npm run build
```

### 2. 环境配置

创建生产环境配置文件：

**根目录 .env**
```env
NODE_ENV=production
PORT=3001
ADMIN_PASSWORD=your-secure-password-here
JWT_SECRET=your-super-secret-jwt-key-at-least-32-chars
SESSION_SECRET=your-session-secret-at-least-32-chars
CORS_ORIGIN=https://yourdomain.com
DATABASE_PATH=./data/subscriptions.db
LOG_LEVEL=warn
```

**后端 backend/.env**
```env
NODE_ENV=production
PORT=3001
ADMIN_PASSWORD=your-secure-password-here
JWT_SECRET=your-super-secret-jwt-key-at-least-32-chars
CORS_ORIGIN=https://yourdomain.com
DATABASE_PATH=./data/subscriptions.db
LOG_LEVEL=warn
```

### 3. 启动生产服务器

#### 方式一：使用批处理脚本
```bash
start-production.bat
```

#### 方式二：手动启动
```bash
cd backend
npm run start
```

#### 方式三：使用PM2（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start backend/dist/index.js --name "airport-subscription-api"

# 查看状态
pm2 status

# 查看日志
pm2 logs airport-subscription-api
```

### 4. 反向代理配置

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    # 前端静态文件
    location / {
        root /path/to/your/project/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Docker部署

### 1. 创建Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/

RUN npm ci
RUN cd frontend && npm ci
RUN cd backend && npm ci

COPY . .
RUN npm run build

# 生产镜像
FROM node:18-alpine AS production

WORKDIR /app

# 只复制必要的文件
COPY --from=builder /app/backend/dist ./backend/dist
COPY --from=builder /app/backend/package*.json ./backend/
COPY --from=builder /app/frontend/dist ./frontend/dist

# 安装生产依赖
RUN cd backend && npm ci --only=production

EXPOSE 3001

CMD ["node", "backend/dist/index.js"]
```

### 2. 构建和运行
```bash
# 构建镜像
docker build -t airport-subscription-manager .

# 运行容器
docker run -d \
  --name airport-subscription \
  -p 3001:3001 \
  -e NODE_ENV=production \
  -e ADMIN_PASSWORD=your-secure-password \
  -v $(pwd)/data:/app/data \
  airport-subscription-manager
```

## 安全建议

### 1. 环境变量安全
- 使用强密码（至少32位字符）
- 定期更换密码和密钥
- 不要在代码中硬编码敏感信息

### 2. 网络安全
- 使用HTTPS
- 配置防火墙
- 限制API访问频率

### 3. 数据安全
- 定期备份数据库
- 设置适当的文件权限
- 监控异常访问

## 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
pm2 logs airport-subscription-api

# 查看系统资源
pm2 monit
```

### 2. 健康检查
```bash
# 检查API状态
curl http://localhost:3001/api/subscriptions
```

### 3. 数据备份
```bash
# 备份数据库
cp backend/subscriptions.db backup/subscriptions_$(date +%Y%m%d_%H%M%S).db
```
