import axios from 'axios';

export interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  totalTraffic: number;
  expiryDate: string;
  updateInterval: number;
  subConverter?: string; // 订阅转换后端
  subConfig?: string; // 订阅配置文件
  token?: string; // 访问令牌
  guestToken?: string; // 访客令牌
  subscriptionDays?: number; // 订阅周期天数
  botToken?: string; // Telegram Bot Token
  chatId?: string; // Telegram Chat ID
}

export interface SubscriptionInfo {
  upload: number;
  download: number;
  total: number;
  expire: number;
}

export class NodePoolService {
  private config: NodePoolConfig;
  private subConverter: string;
  private subConfig: string;
  private subProtocol: string;

  constructor(config: NodePoolConfig) {
    this.config = config;

    // 设置默认的订阅转换后端和配置
    this.subConverter = config.subConverter || 'SUBAPI.cmliussss.net';
    this.subConfig = config.subConfig || 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini';

    // 处理协议
    if (this.subConverter.includes('http://')) {
      this.subConverter = this.subConverter.replace('http://', '');
      this.subProtocol = 'http';
    } else if (this.subConverter.includes('https://')) {
      this.subConverter = this.subConverter.replace('https://', '');
      this.subProtocol = 'https';
    } else {
      // 如果没有协议前缀，默认使用https
      this.subProtocol = 'https';
    }
  }

  // 获取所有节点
  async getAllNodes(): Promise<string[]> {
    const nodes: string[] = [];

    // 添加直接配置的节点
    if (this.config.nodeLinks) {
      const directNodes = this.parseNodeLinks(this.config.nodeLinks);
      nodes.push(...directNodes);
    }

    // 获取订阅链接中的节点
    if (this.config.subscriptionLinks) {
      const subscriptionNodes = await this.fetchSubscriptionNodes();
      nodes.push(...subscriptionNodes);
    }

    // 不进行去重，只过滤空行
    return nodes.filter(node => node.trim() !== '');
  }

  // 解析直接配置的节点链接
  private parseNodeLinks(nodeLinks: string): string[] {
    return nodeLinks
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && this.isValidNodeLink(line));
  }

  // 验证节点链接格式
  private isValidNodeLink(link: string): boolean {
    const protocols = ['vmess://', 'vless://', 'trojan://', 'ss://', 'ssr://', 'hysteria://', 'tuic://'];
    return protocols.some(protocol => link.startsWith(protocol));
  }

  // 获取订阅链接中的节点
  private async fetchSubscriptionNodes(): Promise<string[]> {
    const subscriptionUrls = this.config.subscriptionLinks
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && line.startsWith('http'));

    const allNodes: string[] = [];

    for (const url of subscriptionUrls) {
      try {
        console.log(`获取订阅: ${url}`);
        const nodes = await this.fetchSingleSubscription(url);
        allNodes.push(...nodes);
      } catch (error) {
        console.error(`获取订阅失败 ${url}:`, error);
      }
    }

    // 不进行去重，保持原始顺序
    return allNodes;
  }

  // 获取单个订阅
  private async fetchSingleSubscription(url: string): Promise<string[]> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'v2rayN/6.45 cmliu/CF-Workers-SUB'
        }
      });

      let content = response.data;

      // 检查是否是base64编码
      if (this.isBase64(content)) {
        content = Buffer.from(content, 'base64').toString('utf-8');
      }

      // 解析节点
      return content
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line && this.isValidNodeLink(line));
    } catch (error) {
      console.error(`获取订阅失败: ${url}`, error);
      return [];
    }
  }

  // 检查是否是base64编码
  private isBase64(str: string): boolean {
    try {
      // 清理字符串，移除空白字符
      const cleanStr = str.replace(/[\r\n\s]/g, '');

      // 检查长度是否为4的倍数
      if (cleanStr.length % 4 !== 0) return false;

      // 检查是否只包含base64字符
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(cleanStr)) return false;

      // 尝试解码并检查是否包含节点协议
      const decoded = Buffer.from(cleanStr, 'base64').toString('utf-8');

      // 检查解码后的内容是否包含常见的节点协议
      const protocols = ['vmess://', 'vless://', 'trojan://', 'ss://', 'ssr://', 'hysteria://', 'tuic://'];
      return protocols.some(protocol => decoded.includes(protocol));
    } catch {
      return false;
    }
  }

  // 生成base64订阅
  async generateBase64Subscription(): Promise<string> {
    const nodes = await this.getAllNodes();
    const content = nodes.join('\n');
    return Buffer.from(content, 'utf-8').toString('base64');
  }

  // 生成指定格式的订阅
  async generateSubscription(format: string, baseUrl: string): Promise<string> {
    const nodes = await this.getAllNodes();
    const base64Content = Buffer.from(nodes.join('\n'), 'utf-8').toString('base64');

    // 如果是base64格式或者没有指定格式（自适应订阅），直接返回base64
    if (format === 'base64' || !format || format === 'undefined') {
      console.log('返回base64格式，format:', format);
      return base64Content;
    }

    // 构建订阅转换URL
    const subscriptionUrl = `${baseUrl}/api/nodepool/raw?token=auto`;

    try {
      const subConverterUrl = this.buildConverterUrl(format, subscriptionUrl);
      console.log('使用订阅转换URL:', subConverterUrl);

      const response = await axios.get(subConverterUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'ClashforWindows/0.20.39'
        }
      });

      if (response.status === 200) {
        let content = response.data;

        // 如果是Clash格式，进行修复
        if (format === 'clash') {
          content = this.fixClashConfig(content);
        }

        // 注意：我们只返回内容，不传递第三方服务的响应头
        // 这样可以避免Content-Disposition: attachment等下载响应头
        return content;
      } else {
        console.error('订阅转换失败，状态码:', response.status);
        return base64Content; // 转换失败时返回base64
      }
    } catch (error) {
      console.error('订阅转换失败:', error);
      return base64Content; // 转换失败时返回base64
    }
  }

  // 构建订阅转换URL
  private buildConverterUrl(format: string, subscriptionUrl: string): string {
    const encodedUrl = encodeURIComponent(subscriptionUrl);
    const encodedConfig = encodeURIComponent(this.subConfig);

    switch (format) {
      case 'clash':
        return `${this.subProtocol}://${this.subConverter}/sub?target=clash&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'singbox':
        return `${this.subProtocol}://${this.subConverter}/sub?target=singbox&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'surge':
        return `${this.subProtocol}://${this.subConverter}/sub?target=surge&ver=4&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'quanx':
        return `${this.subProtocol}://${this.subConverter}/sub?target=quanx&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&udp=true`;
      case 'loon':
        return `${this.subProtocol}://${this.subConverter}/sub?target=loon&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false`;
      default:
        return `${this.subProtocol}://${this.subConverter}/sub?target=clash&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
    }
  }

  // 修复Clash配置
  private fixClashConfig(content: string): string {
    if (content.includes('wireguard') && !content.includes('remote-dns-resolve')) {
      let lines: string[];
      if (content.includes('\r\n')) {
        lines = content.split('\r\n');
      } else {
        lines = content.split('\n');
      }

      let result = "";
      for (let line of lines) {
        if (line.includes('type: wireguard')) {
          const oldContent = `, mtu: 1280, udp: true`;
          const newContent = `, mtu: 1280, remote-dns-resolve: true, udp: true`;
          result += line.replace(new RegExp(oldContent, 'g'), newContent) + '\n';
        } else {
          result += line + '\n';
        }
      }
      content = result;
    }
    return content;
  }

  // 生成Clash配置
  async generateClashConfig(): Promise<string> {
    const nodes = await this.getAllNodes();
    
    const clashConfig = {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'Rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: [],
      'proxy-groups': [
        {
          name: '节点选择',
          type: 'select',
          proxies: ['自动选择', 'DIRECT']
        },
        {
          name: '自动选择',
          type: 'url-test',
          proxies: [],
          url: 'http://www.gstatic.com/generate_204',
          interval: 300
        }
      ],
      rules: [
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        'IP-CIDR,********/8,DIRECT',
        'IP-CIDR,**********/10,DIRECT',
        'MATCH,节点选择'
      ]
    };

    // 解析节点并添加到配置中
    for (const node of nodes) {
      try {
        const proxy = this.parseNodeToClash(node);
        if (proxy) {
          (clashConfig.proxies as any[]).push(proxy);
          (clashConfig['proxy-groups'][0].proxies as string[]).push(proxy.name);
          (clashConfig['proxy-groups'][1].proxies as string[]).push(proxy.name);
        }
      } catch (error) {
        console.error('解析节点失败:', node, error);
      }
    }

    return JSON.stringify(clashConfig, null, 2);
  }

  // 将节点转换为Clash格式（简化版本）
  private parseNodeToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const protocol = url.protocol.slice(0, -1);

      switch (protocol) {
        case 'vmess':
          return this.parseVmessToClash(nodeUrl);
        case 'vless':
          return this.parseVlessToClash(nodeUrl);
        case 'trojan':
          return this.parseTrojanToClash(nodeUrl);
        case 'ss':
          return this.parseShadowsocksToClash(nodeUrl);
        default:
          return null;
      }
    } catch (error) {
      console.error('解析节点URL失败:', nodeUrl, error);
      return null;
    }
  }

  // 解析VMess节点（简化版本）
  private parseVmessToClash(nodeUrl: string): any {
    try {
      const base64Data = nodeUrl.replace('vmess://', '');
      const config = JSON.parse(Buffer.from(base64Data, 'base64').toString('utf-8'));
      
      return {
        name: config.ps || config.add || 'VMess节点',
        type: 'vmess',
        server: config.add,
        port: parseInt(config.port),
        uuid: config.id,
        alterId: parseInt(config.aid) || 0,
        cipher: 'auto',
        network: config.net || 'tcp',
        tls: config.tls === 'tls'
      };
    } catch (error) {
      console.error('解析VMess节点失败:', error);
      return null;
    }
  }

  // 解析VLESS节点（简化版本）
  private parseVlessToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const params = new URLSearchParams(url.search);
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'vless',
        server: url.hostname,
        port: parseInt(url.port),
        uuid: url.username,
        network: params.get('type') || 'tcp',
        tls: params.get('security') === 'tls'
      };
    } catch (error) {
      console.error('解析VLESS节点失败:', error);
      return null;
    }
  }

  // 解析Trojan节点（简化版本）
  private parseTrojanToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const params = new URLSearchParams(url.search);
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'trojan',
        server: url.hostname,
        port: parseInt(url.port),
        password: url.username,
        sni: params.get('sni') || url.hostname
      };
    } catch (error) {
      console.error('解析Trojan节点失败:', error);
      return null;
    }
  }

  // 解析Shadowsocks节点（简化版本）
  private parseShadowsocksToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const userInfo = Buffer.from(url.username, 'base64').toString('utf-8');
      const [method, password] = userInfo.split(':');
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'ss',
        server: url.hostname,
        port: parseInt(url.port),
        cipher: method,
        password: password
      };
    } catch (error) {
      console.error('解析Shadowsocks节点失败:', error);
      return null;
    }
  }

  // 计算订阅信息
  calculateSubscriptionInfo(): SubscriptionInfo {
    const totalBytes = this.config.totalTraffic * 1099511627776; // TB to bytes
    const expiryTimestamp = new Date(this.config.expiryDate).getTime();
    const currentTime = Date.now();
    const subscriptionDays = this.config.subscriptionDays || 30;

    console.log('开始计算流量信息 --------------');
    console.log('当前时间:', new Date(currentTime).toISOString());
    console.log('过期时间戳:', expiryTimestamp, '对应日期:', new Date(expiryTimestamp).toISOString());
    console.log('订阅周期天数:', subscriptionDays);
    console.log('总流量(字节):', totalBytes, '总流量(TB):', this.config.totalTraffic);

    // 计算已使用流量 - 使用固定的起始日期（与example.txt一致）
    const oneDayMs = 24 * 60 * 60 * 1000; // 一天的毫秒数
    let fixedStartTime = expiryTimestamp - (subscriptionDays * oneDayMs); // 过期时间减去订阅周期

    // 确保起始时间不会超过当前时间（避免出现负值流量）
    if (fixedStartTime > currentTime) {
      console.log('警告：计算的起始时间晚于当前时间，将使用当前时间作为起始时间');
      fixedStartTime = currentTime;
    }

    // 确保起始时间不会早于很久以前（避免异常大的已用流量）
    const maxPastDays = 365; // 最多追溯一年
    const minStartTime = currentTime - (maxPastDays * oneDayMs);
    if (fixedStartTime < minStartTime) {
      console.log('警告：计算的起始时间过早，将限制为最多一年前');
      fixedStartTime = minStartTime;
    }

    console.log('固定起始时间计算: 过期时间戳', expiryTimestamp, '- (订阅周期', subscriptionDays, '* 一天毫秒数', oneDayMs, ') =', fixedStartTime);
    console.log('固定起始时间:', new Date(fixedStartTime).toISOString());

    // 计算已经过去的时间和总订阅时间
    const elapsedTime = Math.max(0, currentTime - fixedStartTime); // 确保不会出现负值
    const totalTime = Math.max(oneDayMs, expiryTimestamp - fixedStartTime); // 确保总时间至少为一天
    console.log('总订阅天数:', (totalTime / oneDayMs).toFixed(2), '天');
    console.log('已过天数:', (elapsedTime / oneDayMs).toFixed(2), '天');
    console.log('时间百分比:', (elapsedTime/totalTime).toFixed(4), ' (', elapsedTime, '/', totalTime, ')');

    // 计算已用流量（基于时间比例）
    let usedBytes = 0;
    if (totalTime > 0) { // 只要总时间大于0就计算
      const elapsedPercent = Math.min(elapsedTime / totalTime, 1); // 限制比例最大为1（100%）
      console.log('elapsedPercent =', elapsedPercent);
      // 确保不超过总流量
      usedBytes = Math.floor(totalBytes * elapsedPercent);
      console.log('usedBytes = totalBytes *', elapsedPercent, '=', usedBytes);
    }

    // 剩余流量
    let remainBytes = totalBytes - usedBytes;
    console.log('remainBytes = totalBytes - usedBytes =', totalBytes, '-', usedBytes, '=', remainBytes);

    // 确保不会出现负值
    if (usedBytes < 0) {
      console.log('usedBytes < 0, 设为0');
      usedBytes = 0;
    }
    if (remainBytes < 0) {
      console.log('remainBytes < 0, 设为0');
      remainBytes = 0;
    }

    console.log('已用流量(字节):', usedBytes,
                '已用流量(TB):', (usedBytes / 1099511627776).toFixed(2),
                '剩余流量(TB):', (remainBytes / 1099511627776).toFixed(2));

    // 处理过期时间
    let expire = Math.floor(expiryTimestamp / 1000);
    console.log('过期时间(秒):', expire, '过期日期:', new Date(expire * 1000).toISOString());
    console.log('流量计算完成 --------------');

    return {
      upload: 0,
      download: usedBytes,
      total: totalBytes,
      expire: expire
    };
  }
}
