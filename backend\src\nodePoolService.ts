import axios from 'axios';

export interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  totalTraffic: number;
  expiryDate: string;
  updateInterval: number;
  subConverter?: string; // 订阅转换后端
  subConfig?: string; // 订阅配置文件
}

export interface SubscriptionInfo {
  upload: number;
  download: number;
  total: number;
  expire: number;
}

export class NodePoolService {
  private config: NodePoolConfig;
  private subConverter: string;
  private subConfig: string;
  private subProtocol: string;

  constructor(config: NodePoolConfig) {
    this.config = config;

    // 设置默认的订阅转换后端和配置
    this.subConverter = config.subConverter || 'SUBAPI.cmliussss.net';
    this.subConfig = config.subConfig || 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini';

    // 处理协议
    if (this.subConverter.includes('http://')) {
      this.subConverter = this.subConverter.split('//')[1];
      this.subProtocol = 'http';
    } else {
      this.subConverter = this.subConverter.split('//')[1] || this.subConverter;
      this.subProtocol = 'https';
    }
  }

  // 获取所有节点
  async getAllNodes(): Promise<string[]> {
    const nodes: string[] = [];

    // 添加直接配置的节点
    if (this.config.nodeLinks) {
      const directNodes = this.parseNodeLinks(this.config.nodeLinks);
      nodes.push(...directNodes);
    }

    // 获取订阅链接中的节点
    if (this.config.subscriptionLinks) {
      const subscriptionNodes = await this.fetchSubscriptionNodes();
      nodes.push(...subscriptionNodes);
    }

    // 不进行去重，只过滤空行
    return nodes.filter(node => node.trim() !== '');
  }

  // 解析直接配置的节点链接
  private parseNodeLinks(nodeLinks: string): string[] {
    return nodeLinks
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && this.isValidNodeLink(line));
  }

  // 验证节点链接格式
  private isValidNodeLink(link: string): boolean {
    const protocols = ['vmess://', 'vless://', 'trojan://', 'ss://', 'ssr://', 'hysteria://', 'tuic://'];
    return protocols.some(protocol => link.startsWith(protocol));
  }

  // 获取订阅链接中的节点
  private async fetchSubscriptionNodes(): Promise<string[]> {
    const subscriptionUrls = this.config.subscriptionLinks
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && line.startsWith('http'));

    const allNodes: string[] = [];

    for (const url of subscriptionUrls) {
      try {
        console.log(`获取订阅: ${url}`);
        const nodes = await this.fetchSingleSubscription(url);
        allNodes.push(...nodes);
      } catch (error) {
        console.error(`获取订阅失败 ${url}:`, error);
      }
    }

    // 不进行去重，保持原始顺序
    return allNodes;
  }

  // 获取单个订阅
  private async fetchSingleSubscription(url: string): Promise<string[]> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'v2rayN/6.45 cmliu/CF-Workers-SUB'
        }
      });

      let content = response.data;

      // 检查是否是base64编码
      if (this.isBase64(content)) {
        content = Buffer.from(content, 'base64').toString('utf-8');
      }

      // 解析节点
      return content
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line && this.isValidNodeLink(line));
    } catch (error) {
      console.error(`获取订阅失败: ${url}`, error);
      return [];
    }
  }

  // 检查是否是base64编码
  private isBase64(str: string): boolean {
    try {
      const cleanStr = str.replace(/\s/g, '');
      const base64Regex = /^[A-Za-z0-9+/=]+$/;
      if (!base64Regex.test(cleanStr)) return false;
      
      const decoded = Buffer.from(cleanStr, 'base64').toString('utf-8');
      return decoded.includes('://');
    } catch {
      return false;
    }
  }

  // 生成base64订阅
  async generateBase64Subscription(): Promise<string> {
    const nodes = await this.getAllNodes();
    const content = nodes.join('\n');
    return Buffer.from(content, 'utf-8').toString('base64');
  }

  // 生成指定格式的订阅
  async generateSubscription(format: string, baseUrl: string): Promise<string> {
    const nodes = await this.getAllNodes();
    const base64Content = Buffer.from(nodes.join('\n'), 'utf-8').toString('base64');

    // 如果是base64格式或者没有指定格式，直接返回base64
    if (format === 'base64' || !format) {
      return base64Content;
    }

    // 构建订阅转换URL
    const subscriptionUrl = `${baseUrl}/api/nodepool/raw?token=auto`;

    try {
      const subConverterUrl = this.buildConverterUrl(format, subscriptionUrl);
      console.log('使用订阅转换URL:', subConverterUrl);

      const response = await axios.get(subConverterUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'ClashforWindows/0.20.39'
        }
      });

      if (response.status === 200) {
        let content = response.data;

        // 如果是Clash格式，进行修复
        if (format === 'clash') {
          content = this.fixClashConfig(content);
        }

        return content;
      } else {
        console.error('订阅转换失败，状态码:', response.status);
        return base64Content; // 转换失败时返回base64
      }
    } catch (error) {
      console.error('订阅转换失败:', error);
      return base64Content; // 转换失败时返回base64
    }
  }

  // 构建订阅转换URL
  private buildConverterUrl(format: string, subscriptionUrl: string): string {
    const encodedUrl = encodeURIComponent(subscriptionUrl);
    const encodedConfig = encodeURIComponent(this.subConfig);

    switch (format) {
      case 'clash':
        return `${this.subProtocol}://${this.subConverter}/sub?target=clash&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'singbox':
        return `${this.subProtocol}://${this.subConverter}/sub?target=singbox&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'surge':
        return `${this.subProtocol}://${this.subConverter}/sub?target=surge&ver=4&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
      case 'quanx':
        return `${this.subProtocol}://${this.subConverter}/sub?target=quanx&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&udp=true`;
      case 'loon':
        return `${this.subProtocol}://${this.subConverter}/sub?target=loon&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false`;
      default:
        return `${this.subProtocol}://${this.subConverter}/sub?target=clash&url=${encodedUrl}&insert=false&config=${encodedConfig}&emoji=true&list=false&tfo=false&scv=true&fdn=false&sort=false&new_name=true`;
    }
  }

  // 修复Clash配置
  private fixClashConfig(content: string): string {
    if (content.includes('wireguard') && !content.includes('remote-dns-resolve')) {
      let lines: string[];
      if (content.includes('\r\n')) {
        lines = content.split('\r\n');
      } else {
        lines = content.split('\n');
      }

      let result = "";
      for (let line of lines) {
        if (line.includes('type: wireguard')) {
          const oldContent = `, mtu: 1280, udp: true`;
          const newContent = `, mtu: 1280, remote-dns-resolve: true, udp: true`;
          result += line.replace(new RegExp(oldContent, 'g'), newContent) + '\n';
        } else {
          result += line + '\n';
        }
      }
      content = result;
    }
    return content;
  }

  // 生成Clash配置
  async generateClashConfig(): Promise<string> {
    const nodes = await this.getAllNodes();
    
    const clashConfig = {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'Rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: [],
      'proxy-groups': [
        {
          name: '节点选择',
          type: 'select',
          proxies: ['自动选择', 'DIRECT']
        },
        {
          name: '自动选择',
          type: 'url-test',
          proxies: [],
          url: 'http://www.gstatic.com/generate_204',
          interval: 300
        }
      ],
      rules: [
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        'IP-CIDR,********/8,DIRECT',
        'IP-CIDR,**********/10,DIRECT',
        'MATCH,节点选择'
      ]
    };

    // 解析节点并添加到配置中
    for (const node of nodes) {
      try {
        const proxy = this.parseNodeToClash(node);
        if (proxy) {
          (clashConfig.proxies as any[]).push(proxy);
          (clashConfig['proxy-groups'][0].proxies as string[]).push(proxy.name);
          (clashConfig['proxy-groups'][1].proxies as string[]).push(proxy.name);
        }
      } catch (error) {
        console.error('解析节点失败:', node, error);
      }
    }

    return JSON.stringify(clashConfig, null, 2);
  }

  // 将节点转换为Clash格式（简化版本）
  private parseNodeToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const protocol = url.protocol.slice(0, -1);

      switch (protocol) {
        case 'vmess':
          return this.parseVmessToClash(nodeUrl);
        case 'vless':
          return this.parseVlessToClash(nodeUrl);
        case 'trojan':
          return this.parseTrojanToClash(nodeUrl);
        case 'ss':
          return this.parseShadowsocksToClash(nodeUrl);
        default:
          return null;
      }
    } catch (error) {
      console.error('解析节点URL失败:', nodeUrl, error);
      return null;
    }
  }

  // 解析VMess节点（简化版本）
  private parseVmessToClash(nodeUrl: string): any {
    try {
      const base64Data = nodeUrl.replace('vmess://', '');
      const config = JSON.parse(Buffer.from(base64Data, 'base64').toString('utf-8'));
      
      return {
        name: config.ps || config.add || 'VMess节点',
        type: 'vmess',
        server: config.add,
        port: parseInt(config.port),
        uuid: config.id,
        alterId: parseInt(config.aid) || 0,
        cipher: 'auto',
        network: config.net || 'tcp',
        tls: config.tls === 'tls'
      };
    } catch (error) {
      console.error('解析VMess节点失败:', error);
      return null;
    }
  }

  // 解析VLESS节点（简化版本）
  private parseVlessToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const params = new URLSearchParams(url.search);
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'vless',
        server: url.hostname,
        port: parseInt(url.port),
        uuid: url.username,
        network: params.get('type') || 'tcp',
        tls: params.get('security') === 'tls'
      };
    } catch (error) {
      console.error('解析VLESS节点失败:', error);
      return null;
    }
  }

  // 解析Trojan节点（简化版本）
  private parseTrojanToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const params = new URLSearchParams(url.search);
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'trojan',
        server: url.hostname,
        port: parseInt(url.port),
        password: url.username,
        sni: params.get('sni') || url.hostname
      };
    } catch (error) {
      console.error('解析Trojan节点失败:', error);
      return null;
    }
  }

  // 解析Shadowsocks节点（简化版本）
  private parseShadowsocksToClash(nodeUrl: string): any {
    try {
      const url = new URL(nodeUrl);
      const userInfo = Buffer.from(url.username, 'base64').toString('utf-8');
      const [method, password] = userInfo.split(':');
      
      return {
        name: decodeURIComponent(url.hash.slice(1)) || url.hostname,
        type: 'ss',
        server: url.hostname,
        port: parseInt(url.port),
        cipher: method,
        password: password
      };
    } catch (error) {
      console.error('解析Shadowsocks节点失败:', error);
      return null;
    }
  }

  // 计算订阅信息
  calculateSubscriptionInfo(): SubscriptionInfo {
    const totalBytes = this.config.totalTraffic * 1099511627776; // TB to bytes
    const expiryTimestamp = new Date(this.config.expiryDate).getTime();
    const currentTime = Date.now();
    
    // 计算已用流量（基于时间比例）
    const totalDays = 30; // 假设30天周期
    const startTime = expiryTimestamp - (totalDays * 24 * 60 * 60 * 1000);
    const elapsedTime = Math.max(0, currentTime - startTime);
    const totalTime = expiryTimestamp - startTime;
    
    let usedBytes = 0;
    if (totalTime > 0 && currentTime < expiryTimestamp) {
      const usedRatio = Math.min(elapsedTime / totalTime, 1);
      usedBytes = Math.floor(totalBytes * usedRatio);
    } else if (currentTime >= expiryTimestamp) {
      usedBytes = totalBytes; // 已过期，显示全部用完
    }

    return {
      upload: 0,
      download: usedBytes,
      total: totalBytes,
      expire: Math.floor(expiryTimestamp / 1000)
    };
  }
}
