import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { adminApi } from '../api';
import { Subscription } from '../types';

interface TrafficRecord {
  id: string;
  subscriptionId: string;
  timestamp: string;
  usedTraffic: number;
  remainingTraffic: number;
  totalTraffic: number;
}

interface ChartData {
  time: string;
  totalTraffic: number;
  usedTraffic: number;
  remainingTraffic: number;
}

export default function AdminAnalytics() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [trafficRecords, setTrafficRecords] = useState<TrafficRecord[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<string | null>(null); // 改为单选
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const subsData = await adminApi.getSubscriptions();
      setSubscriptions(subsData);

      // 获取流量记录数据
      const hours = getHoursForTimeRange();
      let records: TrafficRecord[] = [];

      try {
        const apiRecords = await adminApi.getTrafficRecords(
          selectedSubscription || undefined,
          hours
        );
        records = apiRecords;
      } catch (recordError) {
        console.warn('获取流量记录失败，使用模拟数据:', recordError);
        // 如果API失败，使用模拟数据作为后备
        records = generateMockTrafficRecords(subsData);
      }

      setTrafficRecords(records);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const getHoursForTimeRange = () => {
    switch (timeRange) {
      case '24h': return 24;
      case '7d': return 24 * 7;
      case '30d': return 24 * 30;
      default: return 24;
    }
  };

  // 生成模拟的流量记录数据（作为后备）
  const generateMockTrafficRecords = (subs: Subscription[]): TrafficRecord[] => {
    const records: TrafficRecord[] = [];
    const now = new Date();
    const hoursBack = getHoursForTimeRange();
    
    subs.forEach(sub => {
      for (let i = hoursBack; i >= 0; i--) {
        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
        
        // 模拟流量增长（随机但趋势向上）
        const progress = (hoursBack - i) / hoursBack;
        const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2的随机因子
        const usedTraffic = sub.totalTraffic * progress * randomFactor;
        const remainingTraffic = Math.max(0, sub.totalTraffic - usedTraffic);
        
        records.push({
          id: `${sub.id}-${i}`,
          subscriptionId: sub.id,
          timestamp: timestamp.toISOString(),
          usedTraffic: Math.min(usedTraffic, sub.totalTraffic),
          remainingTraffic,
          totalTraffic: sub.totalTraffic
        });
      }
    });
    
    return records;
  };

  const prepareChartData = (): ChartData[] => {
    if (!trafficRecords.length) return [];

    if (selectedSubscription) {
      // 显示单个订阅的详细数据
      return trafficRecords
        .filter(record => record.subscriptionId === selectedSubscription)
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        .slice(-12) // 只显示最近12个数据点
        .map(record => ({
          time: new Date(record.timestamp).toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          }),
          totalTraffic: record.totalTraffic,
          usedTraffic: record.usedTraffic,
          remainingTraffic: record.remainingTraffic
        }));
    } else {
      // 显示总体数据（所有订阅汇总）
      const timeGroups: { [key: string]: TrafficRecord[] } = {};

      trafficRecords.forEach(record => {
        const timeKey = new Date(record.timestamp).toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });

        if (!timeGroups[timeKey]) {
          timeGroups[timeKey] = [];
        }
        timeGroups[timeKey].push(record);
      });

      // 转换为图表数据格式（汇总所有订阅）
      return Object.entries(timeGroups)
        .sort(([a], [b]) => {
          const timeA = new Date(timeGroups[a][0].timestamp).getTime();
          const timeB = new Date(timeGroups[b][0].timestamp).getTime();
          return timeA - timeB;
        })
        .slice(-12) // 只显示最近12个时间点
        .map(([time, records]) => {
          // 确保每个时间点的数据完整性
          const subscriptionCount = subscriptions.length;
          const recordCount = records.length;

          // 如果记录数量不等于订阅数量，说明数据不完整，跳过或补充
          if (recordCount !== subscriptionCount && subscriptionCount > 0) {
            console.warn(`时间点 ${time} 的记录不完整：期望 ${subscriptionCount} 个，实际 ${recordCount} 个`);
          }

          const totalTraffic = records.reduce((sum, record) => sum + record.totalTraffic, 0);
          const usedTraffic = records.reduce((sum, record) => sum + record.usedTraffic, 0);
          const remainingTraffic = records.reduce((sum, record) => sum + record.remainingTraffic, 0);

          return {
            time,
            totalTraffic,
            usedTraffic,
            remainingTraffic,
            recordCount, // 添加记录数量用于调试
            expectedCount: subscriptionCount
          };
        });
    }
  };

  const getSelectedSubscriptionName = () => {
    if (!selectedSubscription) return '总体数据';
    const subscription = subscriptions.find(sub => sub.id === selectedSubscription);
    return subscription ? subscription.name : '未知订阅';
  };

  const formatTraffic = (gb: number) => {
    if (gb >= 1024) {
      return `${(gb / 1024).toFixed(1)} TB`;
    }
    return `${gb.toFixed(1)} GB`;
  };

  // 当选择的订阅改变时，重新加载数据
  useEffect(() => {
    if (subscriptions.length > 0) {
      loadData();
    }
  }, [selectedSubscription, timeRange]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={loadData}
          className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200"
        >
          重试
        </button>
      </div>
    );
  }

  const chartData = prepareChartData();

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">数据记录</h1>
          <p className="text-gray-500 mt-1">
            当前显示：<span className="font-medium text-gray-700">{getSelectedSubscriptionName()}</span>
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as '24h' | '7d' | '30d')}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <button
            onClick={async () => {
              if (!confirm('智能记录将刷新所有订阅并记录最新流量数据，可能需要几分钟时间。\n\n包含无效链接的订阅会自动跳过，不会影响整体进度。\n\n是否继续？')) {
                return;
              }

              try {
                setLoading(true);
                const result = await adminApi.smartRecordTraffic();
                alert(`智能流量记录完成！\n\n✅ 成功：${result.successCount} 个\n❌ 失败：${result.failureCount} 个\n\n页面数据已自动刷新。`);
                await loadData();
              } catch (error) {
                const errorMsg = error instanceof Error ? error.message : '未知错误';
                if (errorMsg.includes('timeout')) {
                  alert('智能记录超时，这可能是因为网络问题或订阅链接响应缓慢。\n\n建议：\n1. 检查网络连接\n2. 删除无效的订阅链接\n3. 稍后重试');
                } else {
                  alert('智能记录失败：' + errorMsg);
                }
              } finally {
                setLoading(false);
              }
            }}
            disabled={loading}
            className="px-3 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <span>记录中...</span>
              </>
            ) : (
              <>
                <span>📊</span>
                <span>智能记录</span>
              </>
            )}
          </button>
          <button
            onClick={async () => {
              try {
                setLoading(true);
                await adminApi.cleanupRecords();
                alert('数据清理完成！已删除不完整的记录');
                await loadData();
              } catch (error) {
                alert('数据清理失败：' + (error instanceof Error ? error.message : '未知错误'));
              } finally {
                setLoading(false);
              }
            }}
            disabled={loading}
            className="px-3 py-2 bg-orange-600 text-white text-sm rounded-lg hover:bg-orange-700 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <span>清理中...</span>
              </>
            ) : (
              <>
                <span>🧹</span>
                <span>清理数据</span>
              </>
            )}
          </button>
          <button
            onClick={loadData}
            className="px-3 py-2 bg-white text-gray-600 text-sm rounded-lg hover:bg-gray-100 hover:text-gray-900 focus:outline-none transition-colors duration-200 flex items-center space-x-2 border border-gray-200"
          >
            <span>🔄</span>
            <span>刷新数据</span>
          </button>
        </div>
      </div>

      {/* 订阅选择 */}
      <div className="bg-white rounded-lg p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">选择订阅</h3>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-3">
            {/* 总体数据选项 */}
            <button
              onClick={() => setSelectedSubscription(null)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                selectedSubscription === null
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              📊 总体数据
            </button>

            {/* 各个订阅选项 */}
            {subscriptions.map((sub) => (
              <button
                key={sub.id}
                onClick={() => setSelectedSubscription(sub.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  selectedSubscription === sub.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {sub.name}
              </button>
            ))}
          </div>

          <div className="text-sm text-gray-500">
            💡 选择"总体数据"查看所有订阅的汇总趋势，选择具体订阅查看详细的三线图表
          </div>
        </div>
      </div>

      {/* 流量变化图表 */}
      <div className="bg-white rounded-lg p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {selectedSubscription ? `${getSelectedSubscriptionName()} - 详细流量趋势` : '总体流量趋势'}
        </h3>
        {chartData.length > 0 ? (
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 11 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  interval={0}
                />
                <YAxis
                  label={{ value: '流量 (GB)', angle: -90, position: 'insideLeft' }}
                  tick={{ fontSize: 11 }}
                  tickFormatter={(value) => formatTraffic(value)}
                />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    formatTraffic(value),
                    name === 'totalTraffic' ? '总流量' :
                    name === 'usedTraffic' ? '已使用' : '剩余流量'
                  ]}
                  labelFormatter={(label) => `时间: ${label}`}
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '12px'
                  }}
                />

                <Legend
                  formatter={(value: string) => {
                    switch(value) {
                      case 'totalTraffic': return '总流量';
                      case 'usedTraffic': return '已使用';
                      case 'remainingTraffic': return '剩余流量';
                      default: return value;
                    }
                  }}
                  wrapperStyle={{ fontSize: '12px' }}
                />

                {/* 总流量线 */}
                <Line
                  type="monotone"
                  dataKey="totalTraffic"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                  name="totalTraffic"
                />

                {/* 已使用流量线 */}
                <Line
                  type="monotone"
                  dataKey="usedTraffic"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                  name="usedTraffic"
                />

                {/* 剩余流量线 */}
                <Line
                  type="monotone"
                  dataKey="remainingTraffic"
                  stroke="#10b981"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                  name="remainingTraffic"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <div className="text-lg mb-2">📈</div>
            <div>暂无流量记录数据</div>
            <div className="text-sm mt-2">点击"立即记录"按钮开始记录流量数据</div>
          </div>
        )}
      </div>

      {/* 当前数据统计 */}
      <div className="bg-white rounded-lg p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {selectedSubscription ? '订阅详情' : '总体统计'}
        </h3>

        {selectedSubscription ? (
          // 单个订阅的详细信息
          (() => {
            const subscription = subscriptions.find(sub => sub.id === selectedSubscription);
            if (!subscription) return <div className="text-gray-500">订阅不存在</div>;

            const usagePercentage = subscription.totalTraffic > 0
              ? (subscription.usedTraffic / subscription.totalTraffic) * 100
              : 0;

            return (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatTraffic(subscription.totalTraffic)}
                  </div>
                  <div className="text-sm text-blue-600 mt-1">总流量</div>
                </div>

                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatTraffic(subscription.usedTraffic)}
                  </div>
                  <div className="text-sm text-orange-600 mt-1">已使用</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {usagePercentage.toFixed(1)}%
                  </div>
                </div>

                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatTraffic(subscription.remainingTraffic)}
                  </div>
                  <div className="text-sm text-green-600 mt-1">剩余流量</div>
                </div>
              </div>
            );
          })()
        ) : (
          // 总体统计信息
          (() => {
            const totalTraffic = subscriptions.reduce((sum, sub) => sum + sub.totalTraffic, 0);
            const totalUsed = subscriptions.reduce((sum, sub) => sum + sub.usedTraffic, 0);
            const totalRemaining = subscriptions.reduce((sum, sub) => sum + sub.remainingTraffic, 0);
            const overallUsagePercentage = totalTraffic > 0 ? (totalUsed / totalTraffic) * 100 : 0;

            return (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-xl font-bold text-gray-600">
                      {subscriptions.length}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">总订阅数</div>
                  </div>

                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">
                      {formatTraffic(totalTraffic)}
                    </div>
                    <div className="text-sm text-blue-600 mt-1">总流量</div>
                  </div>

                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-xl font-bold text-orange-600">
                      {formatTraffic(totalUsed)}
                    </div>
                    <div className="text-sm text-orange-600 mt-1">已使用</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {overallUsagePercentage.toFixed(1)}%
                    </div>
                  </div>

                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">
                      {formatTraffic(totalRemaining)}
                    </div>
                    <div className="text-sm text-green-600 mt-1">剩余流量</div>
                  </div>
                </div>

                {/* 所有订阅的简要列表 */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">所有订阅概览</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {subscriptions.map((sub) => {
                      const usagePercentage = sub.totalTraffic > 0 ? (sub.usedTraffic / sub.totalTraffic) * 100 : 0;
                      return (
                        <div key={sub.id} className="p-3 bg-gray-50 rounded-lg">
                          <div className="font-medium text-gray-900 text-sm mb-2">{sub.name}</div>
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>{formatTraffic(sub.usedTraffic)}</span>
                            <span>{formatTraffic(sub.totalTraffic)}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                usagePercentage > 90 ? 'bg-red-500' :
                                usagePercentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1 text-center">
                            {usagePercentage.toFixed(1)}%
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })()
        )}
      </div>
    </div>
  );
}
