@echo off
echo 🔨 测试项目构建...
echo.

echo 检查依赖是否已安装...
if not exist node_modules (
    echo ❌ 根目录依赖未安装，请先运行 setup.bat
    pause
    exit /b 1
)

if not exist frontend\node_modules (
    echo ❌ 前端依赖未安装，请先运行 setup.bat
    pause
    exit /b 1
)

if not exist backend\node_modules (
    echo ❌ 后端依赖未安装，请先运行 setup.bat
    pause
    exit /b 1
)

echo.
echo 1. 清理之前的构建文件...
if exist frontend\dist rmdir /s /q frontend\dist
if exist backend\dist rmdir /s /q backend\dist
echo ✅ 清理完成

echo.
echo 2. 测试前端构建...
cd frontend
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败！
    cd ..
    pause
    exit /b 1
)
echo ✅ 前端构建成功

echo.
echo 3. 测试后端构建...
cd ..\backend
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 后端构建失败！
    cd ..
    pause
    exit /b 1
)
echo ✅ 后端构建成功

echo.
echo 4. 检查构建输出...
cd ..
if exist frontend\dist (
    echo ✅ 前端构建文件存在
) else (
    echo ❌ 前端构建文件不存在
)

if exist backend\dist (
    echo ✅ 后端构建文件存在
) else (
    echo ❌ 后端构建文件不存在
)

echo.
echo 🎉 构建测试完成！
echo.
echo 📁 构建输出：
echo   frontend\dist\ - 前端静态文件
echo   backend\dist\  - 后端编译文件
echo.
pause
