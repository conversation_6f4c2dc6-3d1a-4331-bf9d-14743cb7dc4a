import React, { Component, ErrorInfo, ReactNode } from 'react';
import { CenteredLayout } from './Layout';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export default class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('错误边界捕获到错误:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <CenteredLayout>
          <div className="card text-center">
              <div className="text-red-600 text-6xl mb-4">⚠️</div>
              <h2 className="text-xl font-bold text-primary-900 mb-2">
                出现了一些问题
              </h2>
              <p className="text-primary-600 mb-4">
                应用遇到了意外错误，请刷新页面重试。
              </p>
              <div className="space-y-2">
                <button
                  onClick={() => window.location.reload()}
                  className="btn btn-primary w-full"
                >
                  刷新页面
                </button>
                <button
                  onClick={() => this.setState({ hasError: false })}
                  className="btn btn-secondary w-full"
                >
                  重试
                </button>
              </div>
              {this.state.error && (
                <details className="mt-4 text-left">
                  <summary className="text-sm text-primary-500 cursor-pointer">
                    错误详情
                  </summary>
                  <pre className="text-xs text-red-600 mt-2 p-2 bg-red-50 rounded overflow-auto">
                    {this.state.error.message}
                  </pre>
                </details>
              )}
          </div>
        </CenteredLayout>
      );
    }

    return this.props.children;
  }
}
