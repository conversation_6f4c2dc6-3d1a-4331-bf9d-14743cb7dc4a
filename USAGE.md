# 使用指南

## 用户前端使用

### 访问方式
直接访问：http://localhost:3000

### 功能说明
- **查看订阅**：显示所有可用的机场订阅
- **流量信息**：查看每个订阅的剩余流量和总流量
- **状态显示**：订阅状态（正常/已过期/已暂停）
- **进度条**：直观显示流量使用情况

### 界面特色
- 简约的灰白色设计风格
- 卡片式布局，信息清晰
- 响应式设计，支持移动设备
- 无管理入口，确保普通用户无法误操作

## 管理员后台使用

### 访问方式
⚠️ **重要**：管理后台没有前端入口按钮，需要直接访问：
```
http://localhost:3000/admin
```

### 登录认证
- **默认密码**：`admin123`
- 登录后状态会保存在浏览器中
- 可以通过"退出登录"按钮退出

### 管理功能

#### 1. 查看订阅列表
- 显示所有订阅的完整信息
- 包括订阅名称、URL、流量使用、到期时间等
- 支持状态筛选和排序

#### 2. 添加新订阅
- 点击"添加订阅"按钮
- **智能方式**（推荐）：
  1. 输入订阅链接
  2. 点击"获取信息"按钮
  3. 系统自动解析并填充订阅信息
  4. **查看流量信息**：系统会显示详细的流量使用情况卡片，包括：
     - 总流量、已用流量、剩余流量
     - 流量使用进度条（颜色表示使用程度）
     - 订阅状态（正常/已过期/已暂停）
  5. 确认信息无误后提交
- **手动方式**：
  - 订阅名称（必填）
  - 订阅链接（必填）
  - 总流量（GB，必填）
  - 到期时间（必填）

**流量信息显示特性**：
- 📊 **可视化展示**：卡片式布局显示流量详情
- 🎨 **智能配色**：进度条根据使用程度变色（绿色→黄色→红色）
- 🔒 **只读显示**：流量数据无法手动编辑，确保真实性
- ⚡ **实时更新**：每次点击"获取信息"都会刷新最新数据

#### 🚀 智能订阅解析功能
系统可以自动从订阅链接获取以下信息：
- **订阅名称**：从域名或配置中提取
- **流量信息**：总流量、已用流量、剩余流量
- **到期时间**：订阅过期时间
- **订阅状态**：正常/已过期/已暂停
- **节点数量**：自动统计可用节点数

**支持的订阅格式**：
- Clash 配置文件
- V2Ray 订阅
- Shadowsocks 订阅
- 标准 subscription-userinfo 头部信息

**智能状态判断**：
- **正常 (active)**：订阅未过期且流量未超额
- **已过期 (expired)**：订阅已过期
- **已暂停 (suspended)**：流量已超额使用

#### 3. 编辑订阅
- 点击订阅列表中的"编辑"按钮
- 可以修改订阅名称、链接、总流量和到期时间
- ⚠️ **注意**：已用流量和订阅状态无法手动编辑，只能通过解析获取

#### 4. 刷新订阅信息
- 点击订阅列表中的"🔄"按钮
- 系统会重新解析订阅链接，更新流量使用情况
- 建议定期刷新以获取最新的流量数据

#### 5. 删除订阅
- 点击订阅列表中的"删除"按钮
- 会弹出确认对话框
- 删除后无法恢复

### 导航功能
- **返回首页**：回到用户前端页面
- **退出登录**：退出管理员模式

## 安全建议

### 生产环境部署
1. **修改默认密码**
   - 编辑 `frontend/src/config.ts`
   - 将 `ADMIN_PASSWORD` 改为强密码

2. **隐藏管理入口**
   - 当前设计已经隐藏了管理入口
   - 只有知道 `/admin` 路径的人才能访问

3. **网络安全**
   - 在生产环境中使用 HTTPS
   - 考虑添加 IP 白名单
   - 实施更强的身份验证

### 数据备份
- 数据库文件：`backend/subscriptions.db`
- 定期备份此文件以防数据丢失
- 可以通过复制文件来迁移数据

## 常见操作

### 重置数据
如果需要清空所有数据：
1. 停止服务器
2. 删除 `backend/subscriptions.db` 文件
3. 重启服务器，会自动创建新数据库并插入示例数据

### 修改端口
- 前端端口：修改 `frontend/vite.config.ts`
- 后端端口：修改 `backend/src/index.ts`

### 自定义样式
- 主要样式文件：`frontend/src/index.css`
- 颜色配置：`frontend/tailwind.config.js`
- 组件样式：各个 `.tsx` 文件中的 className

## 技术支持

如遇到问题，请参考：
1. `README.md` - 基本安装和配置
2. `SECURITY.md` - 安全相关说明
3. 浏览器开发者工具 - 查看错误信息
