# 节点池调试功能说明

## 问题描述
用户反馈节点池功能导入到软件里是空节点，需要诊断节点获取和解析过程中的问题。

## 添加的调试功能

### 1. 详细日志输出
在NodePoolService中添加了详细的控制台日志：

#### getAllNodes() 方法
- 显示配置状态（是否有节点链接/订阅链接配置）
- 显示从各个来源获取的节点数量
- 显示最终有效节点总数
- 当没有节点时显示警告和配置内容

#### fetchSubscriptionNodes() 方法
- 显示找到的订阅链接数量和具体URL
- 显示每个订阅获取的节点数量
- 显示所有订阅的总节点数

#### fetchSingleSubscription() 方法
- 显示请求状态和响应信息
- 显示原始内容长度和前100字符
- 显示是否为base64编码及解码过程
- 显示分割后的行数和有效节点数量
- 显示第一个有效节点的预览

### 2. 调试API接口
新增 `/api/nodepool/debug` 接口，提供详细的诊断信息：

#### 请求方式
```
GET /api/nodepool/debug?token=your_token
```

#### 返回信息
```json
{
  "config": {
    "nodeLinks": "配置状态",
    "subscriptionLinks": "配置状态", 
    "token": "当前token",
    "subConverter": "转换服务地址",
    "subConfig": "配置文件URL"
  },
  "nodeLinksDetail": {
    "content": "完整的节点链接内容",
    "lines": "行数"
  },
  "subscriptionLinksDetail": {
    "content": "完整的订阅链接内容",
    "lines": "行数",
    "urls": ["解析出的HTTP链接列表"]
  },
  "result": {
    "totalNodes": "总节点数",
    "sampleNodes": [
      {
        "protocol": "节点协议",
        "preview": "节点内容预览"
      }
    ]
  }
}
```

### 3. 修复的问题

#### Token问题
修复了订阅转换URL中硬编码token的问题：
```typescript
// 修复前
const subscriptionUrl = `${baseUrl}/api/nodepool/raw?token=auto`;

// 修复后
const token = this.config.token || 'auto';
const subscriptionUrl = `${baseUrl}/api/nodepool/raw?token=${token}`;
```

### 4. 测试页面增强
在 `/nodepool/test` 页面中添加了调试链接，方便快速访问调试信息。

## 使用方法

### 1. 查看服务器日志
启动服务器后，访问任何节点池相关的API都会在控制台输出详细日志：
```bash
npm run dev
# 然后访问订阅链接，查看控制台输出
```

### 2. 使用调试API
访问调试接口获取详细信息：
```
http://localhost:3001/api/nodepool/debug?token=auto
```

### 3. 使用测试页面
访问测试页面，点击"调试信息"链接：
```
http://localhost:3001/nodepool/test
```

## 常见问题诊断

### 1. 没有配置节点源
调试信息显示：
```json
{
  "config": {
    "nodeLinks": "未配置",
    "subscriptionLinks": "未配置"
  }
}
```
**解决方案**：在管理员面板的节点池配置中添加节点链接或订阅链接。

### 2. 订阅链接无效
日志显示：
```
找到 0 个订阅链接: []
```
**解决方案**：检查订阅链接格式，确保以 `http://` 或 `https://` 开头。

### 3. 订阅内容为空
日志显示：
```
从 https://example.com/sub 获取到 0 个节点
有效节点数量: 0
```
**解决方案**：
- 检查订阅链接是否可访问
- 检查订阅内容格式是否正确
- 检查网络连接

### 4. Base64解码问题
日志显示：
```
检测到base64编码，正在解码...
解码后内容长度: 0 字符
```
**解决方案**：订阅内容可能不是有效的base64格式，或者解码后为空。

### 5. 节点格式不支持
日志显示：
```
分割后共 10 行
有效节点数量: 0
```
**解决方案**：检查节点是否使用支持的协议（vmess、vless、trojan、ss、ssr、hysteria、tuic）。

## 支持的节点协议
- vmess://
- vless://
- trojan://
- ss://
- ssr://
- hysteria://
- tuic://

## 下一步调试建议

1. **首先访问调试API**，查看配置和节点获取情况
2. **检查服务器日志**，查看详细的获取过程
3. **验证订阅链接**，确保可以直接访问并返回有效内容
4. **检查节点格式**，确保使用支持的协议
5. **测试单个订阅**，逐个排查问题订阅源

通过这些调试工具，应该能够快速定位节点池为空的具体原因。
