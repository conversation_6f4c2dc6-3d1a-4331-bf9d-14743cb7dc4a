# 机场订阅管理系统

一个简约的机场订阅信息管理系统，包含用户前端和管理员后台。

## 功能特性

### 用户前端
- 查看订阅名称和流量剩余
- 简约的灰白色设计
- 响应式布局
- 无管理入口，确保安全性

### 管理员后台
- 🔒 隐藏式访问（无前端入口按钮）
- 密码保护登录
- 📊 **侧栏式管理界面**：现代化的管理体验
- 🏠 **系统总览**：统计信息和流量使用概览
- 📋 **订阅管理**：添加、编辑、删除订阅
- 📈 **数据记录**：流量变化折线图和历史数据分析
- 🚀 **智能订阅解析**：自动获取真实订阅信息
- 🎨 **智能进度条**：根据使用程度自动变色
- 🔄 **自动流量记录**：每小时自动记录流量数据
- ⏰ **多时间范围**：支持24小时、7天、30天数据查看
- 🚫 **只读流量数据**：防止手动篡改，确保数据真实性

## 技术栈

- **前端**: React + TypeScript + Vite + Tailwind CSS
- **后端**: Node.js + Express + TypeScript
- **数据库**: SQLite
- **状态管理**: React Context

## 快速开始

### 方法一：使用批处理脚本（Windows）

```bash
# 运行安装脚本
setup.bat

# 启动项目
start.bat
```

### 方法二：手动安装

```bash
# 1. 安装根目录依赖
npm install

# 2. 安装后端依赖
cd backend
npm install

# 3. 安装前端依赖
cd ../frontend
npm install

# 4. 返回根目录
cd ..
```

### 开发模式

```bash
# 同时启动前端和后端
npm run dev

# 或者分别启动
npm run dev:frontend  # 前端 (http://localhost:3000)
npm run dev:backend   # 后端 (http://localhost:3001)
```

### 构建生产版本

```bash
npm run build
```

### 访问地址

- **用户前端**：http://localhost:3000
- **管理后台**：http://localhost:3000/admin （需要直接访问此地址）
- **后端API**：http://localhost:3001

> 注意：管理后台没有入口按钮，需要直接在浏览器地址栏输入 `/admin` 路径访问

### 管理员登录

管理后台需要密码验证：
- **默认密码**: `admin123`
- 可通过环境变量 `ADMIN_PASSWORD` 修改密码
- 登录状态会保存在浏览器本地存储中

### 环境配置

项目支持通过环境变量进行配置：

1. 复制环境变量示例文件：
   ```bash
   cp .env.example .env
   cp backend/.env.example backend/.env
   ```

2. 修改 `.env` 文件中的配置项

### 安全说明

⚠️ **重要**: 当前认证机制仅适用于开发和演示环境。生产环境请参考 `SECURITY.md` 文档实施更强的安全措施。

## 故障排除

### 常见问题

1. **React Hooks 错误**
   - 如果遇到 "Rendered more hooks than during the previous render" 错误
   - 这通常是由于组件中条件渲染导致的
   - 解决方案：刷新页面或重启开发服务器

2. **端口占用**
   - 前端默认端口：3000
   - 后端默认端口：3001
   - 如果端口被占用，可以修改 `vite.config.ts` 和 `backend/src/index.ts` 中的端口配置

3. **依赖安装失败**
   - 确保 Node.js 版本 >= 16
   - 尝试删除 `node_modules` 文件夹后重新安装
   - 使用 `npm cache clean --force` 清理缓存

4. **数据库问题**
   - 数据库文件位于 `backend/subscriptions.db`
   - 如果数据异常，可以删除此文件，重启后端会自动重新创建

### 测试构建

运行 `test-build.bat` 来测试项目是否能正常构建。

## 项目结构

```
├── frontend/           # React 前端应用
│   ├── src/
│   │   ├── components/ # React 组件
│   │   ├── api.ts      # API 调用
│   │   └── types.ts    # TypeScript 类型
├── backend/            # Express 后端 API
│   ├── src/
│   │   ├── database.ts # 数据库操作
│   │   └── index.ts    # 服务器入口
├── shared/             # 共享类型定义
└── package.json        # 项目配置
```

## API 接口

### 用户接口
- `GET /api/subscriptions` - 获取订阅列表（简化版）

### 管理员接口
- `GET /api/admin/subscriptions` - 获取所有订阅
- `GET /api/admin/subscriptions/:id` - 获取单个订阅
- `POST /api/admin/subscriptions` - 创建订阅
- `PUT /api/admin/subscriptions/:id` - 更新订阅
- `DELETE /api/admin/subscriptions/:id` - 删除订阅

## 数据库

使用 SQLite 数据库，数据文件为 `backend/subscriptions.db`。

### 订阅表结构

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键 |
| name | TEXT | 订阅名称 |
| url | TEXT | 订阅链接 |
| totalTraffic | REAL | 总流量 (GB) |
| usedTraffic | REAL | 已用流量 (GB) |
| remainingTraffic | REAL | 剩余流量 (GB) |
| expiryDate | TEXT | 到期时间 |
| status | TEXT | 状态 (active/expired/suspended) |
| createdAt | TEXT | 创建时间 |
| updatedAt | TEXT | 更新时间 |

## 许可证

MIT License
