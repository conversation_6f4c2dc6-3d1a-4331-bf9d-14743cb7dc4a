<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机场订阅管理系统 - 项目演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #334155;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #64748b;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .feature-card h3 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .feature-card ul {
            list-style: none;
        }
        
        .feature-card li {
            padding: 0.5rem 0;
            color: #64748b;
        }
        
        .feature-card li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .tech-stack {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 3rem;
        }
        
        .tech-stack h3 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .tech-item {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .tech-item h4 {
            color: #475569;
            margin-bottom: 0.5rem;
        }
        
        .tech-item p {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .setup-guide {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .setup-guide h3 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .setup-steps {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin: 1rem 0;
        }
        
        .setup-steps ol {
            margin-left: 1rem;
        }
        
        .setup-steps li {
            margin: 0.5rem 0;
            color: #475569;
        }
        
        .code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 0.5rem 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: #dbeafe;
            color: #1e40af;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛫 机场订阅管理系统</h1>
            <p>简约、高效的机场订阅信息管理解决方案</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>👥 用户前端</h3>
                <ul>
                    <li>查看订阅名称</li>
                    <li>显示流量剩余</li>
                    <li>简约灰白色设计</li>
                    <li>响应式布局</li>
                    <li>实时数据更新</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>⚙️ 管理后台</h3>
                <ul>
                    <li>🚀 智能订阅解析</li>
                    <li>添加新订阅</li>
                    <li>编辑订阅信息</li>
                    <li>删除订阅</li>
                    <li>查看完整信息</li>
                    <li>流量使用统计</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎨 智能特性</h3>
                <ul>
                    <li>自动解析订阅信息</li>
                    <li>智能提取流量数据</li>
                    <li>自动识别到期时间</li>
                    <li>节点数量统计</li>
                    <li>状态智能判断</li>
                </ul>
            </div>
        </div>
        
        <div class="tech-stack">
            <h3>🛠️ 技术栈</h3>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>前端</h4>
                    <p>React + TypeScript + Vite + Tailwind CSS</p>
                </div>
                <div class="tech-item">
                    <h4>后端</h4>
                    <p>Node.js + Express + TypeScript</p>
                </div>
                <div class="tech-item">
                    <h4>数据库</h4>
                    <p>SQLite（轻量级，易部署）</p>
                </div>
                <div class="tech-item">
                    <h4>状态管理</h4>
                    <p>React Context + Hooks</p>
                </div>
            </div>
        </div>
        
        <div class="setup-guide">
            <h3>🚀 快速开始</h3>
            
            <div class="highlight">
                <strong>Windows 用户推荐：</strong> 直接运行 <code>setup.bat</code> 安装依赖，然后运行 <code>start.bat</code> 启动项目
            </div>
            
            <div class="setup-steps">
                <h4>手动安装步骤：</h4>
                <ol>
                    <li>安装根目录依赖：<div class="code">npm install</div></li>
                    <li>安装后端依赖：<div class="code">cd backend && npm install</div></li>
                    <li>安装前端依赖：<div class="code">cd ../frontend && npm install</div></li>
                    <li>启动项目：<div class="code">cd .. && npm run dev</div></li>
                </ol>
            </div>
            
            <div class="highlight">
                <strong>访问地址：</strong><br>
                • 用户前端：<a href="http://localhost:3000" target="_blank">http://localhost:3000</a><br>
                • 管理后台：<a href="http://localhost:3000/admin" target="_blank">http://localhost:3000/admin</a> （需直接访问）<br>
                • 后端API：<a href="http://localhost:3001" target="_blank">http://localhost:3001</a><br>
                <small style="color: #64748b;">注意：管理后台没有入口按钮，需要直接在地址栏输入 /admin</small>
            </div>

            <div class="highlight">
                <strong>🔐 管理员登录：</strong><br>
                • 默认密码：<code>admin123</code><br>
                • 访问管理后台时需要输入密码<br>
                • 登录状态会保存在浏览器中
            </div>
        </div>
    </div>
</body>
</html>
