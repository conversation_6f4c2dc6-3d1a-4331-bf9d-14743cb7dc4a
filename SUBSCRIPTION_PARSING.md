# 订阅解析功能说明

## 功能概述

机场订阅管理系统现在支持智能解析订阅链接，自动获取真实的订阅信息，包括：

- 📊 **流量信息**：总流量、已用流量、剩余流量
- 📅 **到期时间**：订阅过期时间
- 📝 **订阅名称**：智能提取订阅服务名称
- 📈 **订阅状态**：根据到期时间自动判断状态
- 🔢 **节点统计**：自动统计可用节点数量

## 使用方法

### 1. 基本使用流程

1. **进入管理后台**：访问 `/admin` 并使用密码 `admin123` 登录
2. **添加订阅**：点击"添加订阅"按钮
3. **输入订阅链接**：在"订阅链接"字段输入真实的订阅URL
4. **获取信息**：点击"获取信息"按钮
5. **自动填充**：系统自动解析并填充表单
6. **确认提交**：检查信息无误后点击"创建"

### 2. 调试功能

如果解析失败或结果不准确，可以使用调试功能：

1. **测试连接**：点击"🔍 测试连接"按钮
2. **查看调试信息**：系统会显示详细的连接信息
3. **分析问题**：根据调试信息判断问题原因

## 支持的订阅格式

### 1. 标准格式

- **Clash 配置文件**：YAML格式的Clash配置
- **V2Ray 订阅**：Base64编码的节点列表
- **Shadowsocks 订阅**：SS链接列表
- **Trojan 订阅**：Trojan链接列表

### 2. 流量信息获取

系统会尝试从以下HTTP头部获取流量信息：

```
subscription-userinfo: upload=0; download=1234567890; total=107374182400; expire=1640995200
```

支持的头部名称：
- `subscription-userinfo`
- `Subscription-Userinfo`
- `SUBSCRIPTION-USERINFO`
- `userinfo`
- `Userinfo`

### 3. 订阅名称提取

系统会按以下优先级提取订阅名称：

1. **YAML注释**：从配置文件注释中提取
2. **节点名称**：分析节点名称的公共前缀
3. **URL路径**：从订阅链接路径中提取
4. **域名映射**：根据知名机场域名自动识别

## 解析结果示例

### 成功解析示例

```json
{
  "name": "RixCloud",
  "totalTraffic": 100.0,
  "usedTraffic": 25.5,
  "remainingTraffic": 74.5,
  "expiryDate": "2024-12-31T23:59:59.000Z",
  "status": "active"
}
```

### 调试信息示例

```json
{
  "status": 200,
  "contentLength": 12345,
  "contentType": "text/plain; charset=utf-8",
  "userInfo": "upload=0; download=27487790694; total=107374182400; expire=1703980800",
  "contentPreview": "cHJveGllczoKICAtIG5hbWU6ICJIb25nIEtvbmcgMDEi..."
}
```

## 常见问题

### 1. 为什么没有获取到流量信息？

**可能原因：**
- 订阅服务商没有提供 `subscription-userinfo` 头部
- 头部格式不标准
- 网络连接问题

**解决方法：**
1. 使用"🔍 测试连接"功能查看调试信息
2. 检查是否有 `userInfo` 字段
3. 如果没有，说明该订阅不支持流量查询，需要手动输入

### 2. 为什么订阅名称不正确？

**可能原因：**
- 订阅内容中没有明确的名称信息
- 域名不在已知机场列表中

**解决方法：**
1. 系统会尝试多种方式提取名称
2. 如果自动提取的名称不满意，可以手动修改
3. 可以在表单中直接编辑"订阅名称"字段

### 3. 解析失败怎么办？

**可能原因：**
- 订阅链接无效或过期
- 网络连接问题
- 订阅格式不支持

**解决方法：**
1. 检查订阅链接是否正确
2. 使用"🔍 测试连接"查看详细错误信息
3. 如果解析失败，可以手动填写订阅信息

## 技术细节

### 1. 流量单位转换

- 系统自动将字节转换为GB
- 支持大流量订阅（TB级别）
- 精确到小数点后2位

### 2. 时间处理

- 支持Unix时间戳（秒和毫秒）
- 自动转换为ISO 8601格式
- 根据到期时间判断订阅状态

### 3. Base64解码

- 智能检测Base64编码内容
- 支持标准Base64和URL安全Base64
- 自动处理编码错误

## 开发者信息

如需添加对新订阅格式的支持或改进解析逻辑，请参考：

- 后端解析器：`backend/src/subscriptionParser.ts`
- API端点：`/api/admin/parse-subscription`
- 测试端点：`/api/admin/test-subscription`

## 更新日志

- **v1.0.0**：基础解析功能
- **v1.1.0**：添加调试功能和多格式支持
- **v1.2.0**：改进名称提取和错误处理
