# 安全说明

## 管理员认证

本系统为管理后台提供了基础的密码保护功能。

### 默认配置

- **默认密码**: `admin123`
- **认证方式**: 前端密码验证
- **会话管理**: 浏览器本地存储

### 修改密码

要修改管理员密码，请编辑以下文件：

1. **前端密码**: `frontend/src/config.ts`
   ```typescript
   export const config = {
     ADMIN_PASSWORD: '你的新密码',
     // ...
   };
   ```

2. **后端密码** (可选): `backend/src/index.ts`
   ```typescript
   const ADMIN_PASSWORD = '你的新密码';
   ```

### 生产环境建议

⚠️ **重要**: 当前的认证机制仅适用于开发和演示环境。

对于生产环境，建议实施以下安全措施：

1. **后端认证**
   - 将密码验证移至后端
   - 使用加密哈希存储密码
   - 实现JWT或session认证

2. **环境变量**
   - 使用环境变量存储敏感信息
   - 不要在代码中硬编码密码

3. **HTTPS**
   - 在生产环境中使用HTTPS
   - 确保所有敏感数据传输加密

4. **访问控制**
   - 实现IP白名单
   - 添加登录失败限制
   - 实现会话超时

5. **审计日志**
   - 记录管理员操作日志
   - 监控异常访问行为

### 示例：环境变量配置

创建 `.env` 文件：

```bash
# 管理员密码
ADMIN_PASSWORD=your_secure_password_here

# 数据库配置
DB_PATH=./data/subscriptions.db

# 服务器配置
PORT=3001
NODE_ENV=production
```

在代码中使用：

```typescript
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';
```

### 当前限制

- 密码以明文形式存储在前端代码中
- 没有密码复杂度要求
- 没有登录失败保护
- 会话不会自动过期

这些限制使得当前系统仅适用于受信任的内部环境。
