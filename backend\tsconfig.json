{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist"]}