import { useState, useEffect } from 'react';
import { adminApi } from '../api';

interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  totalTraffic: number; // TB
  expiryDate: string;
  updateInterval: number; // 小时
  subConverter: string; // 订阅转换后端
  subConfig: string; // 订阅配置文件
}

interface SubscriptionInfo {
  upload: number;
  download: number;
  total: number;
  expire: number;
}

export default function AdminNodePool() {
  const [config, setConfig] = useState<NodePoolConfig>({
    nodeLinks: '',
    subscriptionLinks: '',
    totalTraffic: 99,
    expiryDate: '2099-12-31',
    updateInterval: 6,
    subConverter: 'SUBAPI.cmliussss.net',
    subConfig: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);

  // 订阅链接生成
  const [generatedLinks, setGeneratedLinks] = useState<{[key: string]: string}>({});
  const [showQRCode, setShowQRCode] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    loadConfig();
    generateSubscriptionLinks();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminApi.getNodePoolConfig();
      setConfig(data);
      
      // 计算订阅信息
      const totalBytes = data.totalTraffic * 1099511627776; // TB to bytes
      const expiryTimestamp = new Date(data.expiryDate).getTime();
      const currentTime = Date.now();
      
      // 简单的时间比例计算已用流量
      const totalDays = 30; // 假设30天周期
      const elapsedDays = Math.max(0, (currentTime - (expiryTimestamp - totalDays * 24 * 60 * 60 * 1000)) / (24 * 60 * 60 * 1000));
      const usedRatio = Math.min(elapsedDays / totalDays, 1);
      const usedBytes = totalBytes * usedRatio;
      
      setSubscriptionInfo({
        upload: 0,
        download: usedBytes,
        total: totalBytes,
        expire: Math.floor(expiryTimestamp / 1000)
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const generateSubscriptionLinks = () => {
    const baseUrl = window.location.origin;
    const token = 'auto'; // 可以从配置中获取
    
    const links = {
      auto: `${baseUrl}/api/nodepool/sub?token=${token}`,
      base64: `${baseUrl}/api/nodepool/sub?token=${token}&format=base64`,
      clash: `${baseUrl}/api/nodepool/sub?token=${token}&format=clash`,
      singbox: `${baseUrl}/api/nodepool/sub?token=${token}&format=singbox`,
      surge: `${baseUrl}/api/nodepool/sub?token=${token}&format=surge`,
      quanx: `${baseUrl}/api/nodepool/sub?token=${token}&format=quanx`,
      loon: `${baseUrl}/api/nodepool/sub?token=${token}&format=loon`
    };
    
    setGeneratedLinks(links);
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      await adminApi.updateNodePoolConfig(config);
      setSuccess('节点池配置保存成功');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess(`${type}订阅链接已复制到剪贴板`);
      setTimeout(() => setSuccess(null), 2000);
    } catch (err) {
      setError('复制失败，请手动复制');
      setTimeout(() => setError(null), 2000);
    }
  };

  const toggleQRCode = (type: string) => {
    setShowQRCode(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">节点池</h1>
          <p className="text-gray-500 mt-1">节点聚合和订阅转换</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-900 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">节点池</h1>
        <p className="text-gray-500 mt-1">节点聚合和订阅转换</p>
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-red-400 mr-3">❌</span>
            <div className="text-red-700 text-sm">{error}</div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-green-400 mr-3">✅</span>
            <div className="text-green-700 text-sm">{success}</div>
          </div>
        </div>
      )}

      {/* 订阅链接展示 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">订阅链接</h2>
          <p className="text-sm text-gray-500">点击链接复制到剪贴板</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(generatedLinks).map(([type, url]) => (
            <div key={type} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-gray-900 capitalize">
                  {type === 'auto' ? '自适应' : type.toUpperCase()}订阅
                </h3>
                <button
                  onClick={() => toggleQRCode(type)}
                  className="text-gray-400 hover:text-gray-600"
                  title="显示二维码"
                >
                  📱
                </button>
              </div>
              <button
                onClick={() => copyToClipboard(url, type === 'auto' ? '自适应' : type.toUpperCase())}
                className="w-full text-left text-sm text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 p-2 rounded border truncate transition-colors duration-200"
              >
                {url}
              </button>
              {showQRCode[type] && (
                <div className="mt-3 p-3 bg-gray-50 rounded text-center">
                  <div className="text-xs text-gray-500">二维码功能需要集成QR码生成库</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 流量信息 */}
      {subscriptionInfo && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-2">流量信息</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-sm text-blue-600 mb-1">总流量</div>
              <div className="text-lg font-semibold text-blue-900">
                {formatBytes(subscriptionInfo.total)}
              </div>
            </div>
            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-sm text-orange-600 mb-1">已用流量</div>
              <div className="text-lg font-semibold text-orange-900">
                {formatBytes(subscriptionInfo.download)}
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-sm text-green-600 mb-1">剩余流量</div>
              <div className="text-lg font-semibold text-green-900">
                {formatBytes(subscriptionInfo.total - subscriptionInfo.download)}
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-sm text-purple-600 mb-1">过期时间</div>
              <div className="text-lg font-semibold text-purple-900">
                {formatDate(subscriptionInfo.expire)}
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>使用进度</span>
              <span>{((subscriptionInfo.download / subscriptionInfo.total) * 100).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (subscriptionInfo.download / subscriptionInfo.total) * 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* 节点池配置 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">节点池配置</h2>
          <p className="text-sm text-gray-500">配置节点链接和订阅源</p>
        </div>

        <div className="space-y-6">
          {/* 节点链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              节点链接
            </label>
            <textarea
              value={config.nodeLinks}
              onChange={(e) => setConfig({ ...config, nodeLinks: e.target.value })}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个节点链接，支持 vmess://、vless://、trojan://、ss:// 等格式"
            />
          </div>

          {/* 订阅链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              订阅链接
            </label>
            <textarea
              value={config.subscriptionLinks}
              onChange={(e) => setConfig({ ...config, subscriptionLinks: e.target.value })}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个订阅链接，支持各种机场订阅"
            />
          </div>

          {/* 订阅转换设置 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅转换后端
              </label>
              <input
                type="text"
                value={config.subConverter}
                onChange={(e) => setConfig({ ...config, subConverter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如：SUBAPI.cmliussss.net"
              />
              <p className="text-xs text-gray-400 mt-1">
                用于转换订阅格式，支持Clash、SingBox等格式
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅配置文件
              </label>
              <input
                type="url"
                value={config.subConfig}
                onChange={(e) => setConfig({ ...config, subConfig: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="配置文件URL"
              />
              <p className="text-xs text-gray-400 mt-1">
                Clash配置文件，用于规则分流和节点分组
              </p>
            </div>
          </div>

          {/* 流量和时间设置 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                总流量 (TB)
              </label>
              <input
                type="number"
                min="1"
                max="10000"
                value={config.totalTraffic}
                onChange={(e) => setConfig({ ...config, totalTraffic: parseInt(e.target.value) || 99 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                过期日期
              </label>
              <input
                type="date"
                value={config.expiryDate}
                onChange={(e) => setConfig({ ...config, expiryDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                更新间隔 (小时)
              </label>
              <input
                type="number"
                min="1"
                max="168"
                value={config.updateInterval}
                onChange={(e) => setConfig({ ...config, updateInterval: parseInt(e.target.value) || 6 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div className="pt-4">
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>保存中...</span>
                </>
              ) : (
                <>
                  <span>💾</span>
                  <span>保存配置</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
