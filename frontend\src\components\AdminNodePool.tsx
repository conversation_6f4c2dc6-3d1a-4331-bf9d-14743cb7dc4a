import { useState, useEffect } from 'react';
import { adminApi } from '../api';

interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  totalTraffic: number; // TB
  expiryDate: string;
  updateInterval: number; // 小时
  subConverter: string; // 订阅转换后端
  subConfig: string; // 订阅配置文件
  token: string; // 访问令牌
  guestToken: string; // 访客令牌
  subscriptionDays: number; // 订阅周期天数
  botToken: string; // Telegram Bot Token
  chatId: string; // Telegram Chat ID
}

interface SubscriptionInfo {
  upload: number;
  download: number;
  total: number;
  expire: number;
}

export default function AdminNodePool() {
  const [config, setConfig] = useState<NodePoolConfig>({
    nodeLinks: '',
    subscriptionLinks: '',
    totalTraffic: 99,
    expiryDate: '2099-12-31',
    updateInterval: 6,
    subConverter: 'SUBAPI.cmliussss.net',
    subConfig: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
    token: 'auto',
    guestToken: '',
    subscriptionDays: 30,
    botToken: '',
    chatId: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);

  // 订阅链接生成
  const [generatedLinks, setGeneratedLinks] = useState<{[key: string]: string}>({});
  const [showQRCode, setShowQRCode] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    loadConfig();
  }, []);

  useEffect(() => {
    generateSubscriptionLinks();
  }, [config.token]);

  const loadConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminApi.getNodePoolConfig();
      setConfig(data);
      
      // 计算订阅信息（与后端逻辑保持一致）
      const totalBytes = data.totalTraffic * 1099511627776; // TB to bytes
      const expiryTimestamp = new Date(data.expiryDate).getTime();
      const currentTime = Date.now();
      const subscriptionDays = data.subscriptionDays || 30;

      // 使用与后端一致的流量计算逻辑
      const oneDayMs = 24 * 60 * 60 * 1000; // 一天的毫秒数
      let fixedStartTime = expiryTimestamp - (subscriptionDays * oneDayMs); // 过期时间减去订阅周期

      // 确保起始时间不会超过当前时间（避免出现负值流量）
      if (fixedStartTime > currentTime) {
        fixedStartTime = currentTime;
      }

      // 确保起始时间不会早于很久以前（避免异常大的已用流量）
      const maxPastDays = 365; // 最多追溯一年
      const minStartTime = currentTime - (maxPastDays * oneDayMs);
      if (fixedStartTime < minStartTime) {
        fixedStartTime = minStartTime;
      }

      // 计算已经过去的时间和总订阅时间
      const elapsedTime = Math.max(0, currentTime - fixedStartTime); // 确保不会出现负值
      const totalTime = Math.max(oneDayMs, expiryTimestamp - fixedStartTime); // 确保总时间至少为一天

      // 计算已用流量（基于时间比例）
      let usedBytes = 0;
      if (totalTime > 0) { // 只要总时间大于0就计算
        const elapsedPercent = Math.min(elapsedTime / totalTime, 1); // 限制比例最大为1（100%）
        usedBytes = Math.floor(totalBytes * elapsedPercent);
      }

      // 确保不会出现负值
      if (usedBytes < 0) {
        usedBytes = 0;
      }
      
      setSubscriptionInfo({
        upload: 0,
        download: usedBytes,
        total: totalBytes,
        expire: Math.floor(expiryTimestamp / 1000)
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const generateSubscriptionLinks = () => {
    const baseUrl = window.location.origin;
    const token = config.token || 'auto'; // 从配置中获取token

    const links = {
      auto: `${baseUrl}/api/nodepool/sub?token=${token}`,
      base64: `${baseUrl}/api/nodepool/sub?token=${token}&format=base64`,
      clash: `${baseUrl}/api/nodepool/sub?token=${token}&format=clash`,
      singbox: `${baseUrl}/api/nodepool/sub?token=${token}&format=singbox`,
      surge: `${baseUrl}/api/nodepool/sub?token=${token}&format=surge`,
      quanx: `${baseUrl}/api/nodepool/sub?token=${token}&format=quanx`,
      loon: `${baseUrl}/api/nodepool/sub?token=${token}&format=loon`
    };

    setGeneratedLinks(links);
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      await adminApi.updateNodePoolConfig(config);
      setSuccess('节点池配置保存成功');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess(`${type}订阅链接已复制到剪贴板`);
      setTimeout(() => setSuccess(null), 2000);
    } catch (err) {
      setError('复制失败，请手动复制');
      setTimeout(() => setError(null), 2000);
    }
  };

  const toggleQRCode = (type: string) => {
    setShowQRCode(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">节点池</h1>
          <p className="text-gray-500 mt-1">节点聚合和订阅转换</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-900 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">节点池</h1>
        <p className="text-gray-500 mt-1">节点聚合和订阅转换</p>
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-red-400 mr-3">❌</span>
            <div className="text-red-700 text-sm">{error}</div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-green-400 mr-3">✅</span>
            <div className="text-green-700 text-sm">{success}</div>
          </div>
        </div>
      )}

      {/* 订阅链接展示 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">订阅链接</h2>
          <p className="text-sm text-gray-500">点击链接复制到剪贴板</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(generatedLinks).map(([type, url]) => (
            <div key={type} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium text-gray-900 capitalize">
                  {type === 'auto' ? '自适应' : type.toUpperCase()}订阅
                </h3>
                <button
                  onClick={() => toggleQRCode(type)}
                  className="text-gray-400 hover:text-gray-600"
                  title="显示二维码"
                >
                  📱
                </button>
              </div>
              <button
                onClick={() => copyToClipboard(url, type === 'auto' ? '自适应' : type.toUpperCase())}
                className="w-full text-left text-sm text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 p-2 rounded border truncate transition-colors duration-200"
              >
                {url}
              </button>
              {showQRCode[type] && (
                <div className="mt-3 p-3 bg-gray-50 rounded text-center">
                  <div className="text-xs text-gray-500">二维码功能需要集成QR码生成库</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 流量信息 */}
      {subscriptionInfo && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-2">流量信息</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-sm text-blue-600 mb-1">总流量</div>
              <div className="text-lg font-semibold text-blue-900">
                {formatBytes(subscriptionInfo.total)}
              </div>
            </div>
            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-sm text-orange-600 mb-1">已用流量</div>
              <div className="text-lg font-semibold text-orange-900">
                {formatBytes(subscriptionInfo.download)}
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-sm text-green-600 mb-1">剩余流量</div>
              <div className="text-lg font-semibold text-green-900">
                {formatBytes(subscriptionInfo.total - subscriptionInfo.download)}
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-sm text-purple-600 mb-1">过期时间</div>
              <div className="text-lg font-semibold text-purple-900">
                {formatDate(subscriptionInfo.expire)}
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>使用进度</span>
              <span>{((subscriptionInfo.download / subscriptionInfo.total) * 100).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (subscriptionInfo.download / subscriptionInfo.total) * 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* 节点池配置 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">节点池配置</h2>
          <p className="text-sm text-gray-500">配置节点链接和订阅源</p>
        </div>

        <div className="space-y-6">
          {/* 节点链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              节点链接
            </label>
            <textarea
              value={config.nodeLinks}
              onChange={(e) => setConfig({ ...config, nodeLinks: e.target.value })}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个节点链接，支持 vmess://、vless://、trojan://、ss:// 等格式"
            />
          </div>

          {/* 订阅链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              订阅链接
            </label>
            <textarea
              value={config.subscriptionLinks}
              onChange={(e) => setConfig({ ...config, subscriptionLinks: e.target.value })}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个订阅链接，支持各种机场订阅"
            />
          </div>

          {/* 订阅转换设置 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅转换后端
              </label>
              <input
                type="text"
                value={config.subConverter}
                onChange={(e) => setConfig({ ...config, subConverter: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如：SUBAPI.cmliussss.net"
              />
              <p className="text-xs text-gray-400 mt-1">
                用于转换订阅格式，支持Clash、SingBox等格式
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅配置文件
              </label>
              <input
                type="url"
                value={config.subConfig}
                onChange={(e) => setConfig({ ...config, subConfig: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="配置文件URL"
              />
              <p className="text-xs text-gray-400 mt-1">
                Clash配置文件，用于规则分流和节点分组
              </p>
            </div>
          </div>

          {/* 流量和时间设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                总流量 (TB)
              </label>
              <input
                type="number"
                min="1"
                max="10000"
                value={config.totalTraffic}
                onChange={(e) => setConfig({ ...config, totalTraffic: parseInt(e.target.value) || 99 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                过期日期
              </label>
              <input
                type="date"
                value={config.expiryDate}
                onChange={(e) => setConfig({ ...config, expiryDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅周期 (天)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={config.subscriptionDays}
                onChange={(e) => setConfig({ ...config, subscriptionDays: parseInt(e.target.value) || 30 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                更新间隔 (小时)
              </label>
              <input
                type="number"
                min="1"
                max="168"
                value={config.updateInterval}
                onChange={(e) => setConfig({ ...config, updateInterval: parseInt(e.target.value) || 6 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 访问控制设置 */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">访问控制</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访问令牌
                </label>
                <input
                  type="text"
                  value={config.token}
                  onChange={(e) => setConfig({ ...config, token: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：auto"
                />
                <p className="text-xs text-gray-400 mt-1">
                  用于订阅链接的访问验证
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访客令牌
                </label>
                <input
                  type="text"
                  value={config.guestToken}
                  onChange={(e) => setConfig({ ...config, guestToken: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="可选，留空自动生成"
                />
                <p className="text-xs text-gray-400 mt-1">
                  访客订阅的专用令牌
                </p>
              </div>
            </div>
          </div>

          {/* Telegram 通知设置 */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">Telegram 通知</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bot Token
                </label>
                <input
                  type="text"
                  value={config.botToken}
                  onChange={(e) => setConfig({ ...config, botToken: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="可选，从 @BotFather 获取"
                />
                <p className="text-xs text-gray-400 mt-1">
                  用于发送访问通知
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chat ID
                </label>
                <input
                  type="text"
                  value={config.chatId}
                  onChange={(e) => setConfig({ ...config, chatId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="可选，从 @userinfobot 获取"
                />
                <p className="text-xs text-gray-400 mt-1">
                  接收通知的聊天ID
                </p>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>保存中...</span>
                </>
              ) : (
                <>
                  <span>💾</span>
                  <span>保存配置</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
