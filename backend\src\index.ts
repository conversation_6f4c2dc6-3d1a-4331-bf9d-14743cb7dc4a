import express from 'express';
import cors from 'cors';
import { config, validateConfig } from './config';
import { errorHandler, notFoundHandler, asyncHandler } from './middleware/errorHandler';
import { Database } from './database';
import { SubscriptionParser } from './subscriptionParser';
import { NodePoolService } from './nodePoolService';
import { ApiResponse } from '../../shared/types';

// 验证配置
validateConfig();

const app = express();
const db = new Database(config.databasePath);
const parser = new SubscriptionParser();

// 中间件
app.use(cors({
  origin: config.corsOrigin,
  credentials: true
}));
app.use(express.json());

// 管理员认证端点
app.post('/api/admin/login', (req, res) => {
  const { password } = req.body;

  if (password === config.adminPassword) {
    const response: ApiResponse<{ success: boolean }> = {
      success: true,
      data: { success: true },
      message: '登录成功'
    };
    res.json(response);
  } else {
    const response: ApiResponse<null> = {
      success: false,
      message: '密码错误'
    };
    res.status(401).json(response);
  }
});

// 解析订阅链接获取信息
app.post('/api/admin/parse-subscription', async (req, res) => {
  try {
    const { url } = req.body;

    console.log('收到解析请求:', url);

    if (!url) {
      const response: ApiResponse<null> = {
        success: false,
        message: '请提供订阅链接'
      };
      return res.status(400).json(response);
    }

    if (!SubscriptionParser.isValidSubscriptionUrl(url)) {
      const response: ApiResponse<null> = {
        success: false,
        message: '无效的订阅链接格式'
      };
      return res.status(400).json(response);
    }

    const subscriptionInfo = await parser.parseSubscription(url);

    console.log('解析完成，返回结果:', subscriptionInfo);

    // 根据解析结果设置不同的消息
    const message = subscriptionInfo.status === 'error'
      ? '订阅链接解析失败，但已提取基本信息'
      : '订阅信息解析成功';

    const response: ApiResponse<typeof subscriptionInfo> = {
      success: true,
      data: subscriptionInfo,
      message
    };
    res.json(response);
  } catch (error) {
    console.error('解析订阅失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: `解析订阅失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
    res.status(500).json(response);
  }
});

// 调试端点：测试订阅链接连通性
app.post('/api/admin/test-subscription', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ success: false, message: '请提供订阅链接' });
    }

    console.log('测试订阅链接:', url);

    const axios = require('axios');
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'ClashforWindows/0.20.39'
      },
      validateStatus: () => true // 接受所有状态码
    });

    const result = {
      success: true,
      data: {
        status: response.status,
        headers: response.headers,
        contentLength: response.data?.length || 0,
        contentType: response.headers['content-type'],
        userInfo: response.headers['subscription-userinfo'] || response.headers['Subscription-Userinfo'],
        contentPreview: typeof response.data === 'string' ? response.data.substring(0, 200) : 'Binary data'
      }
    };

    console.log('测试结果:', result);
    res.json(result);
  } catch (error) {
    console.error('测试失败:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '测试失败'
    });
  }
});

// 获取所有订阅（用户前端）
app.get('/api/subscriptions', async (_req, res) => {
  try {
    const subscriptions = await db.getAllSubscriptions();
    // 只返回用户需要的基本信息
    const userSubscriptions = subscriptions.map(sub => ({
      id: sub.id,
      name: sub.name,
      url: sub.url,
      remainingTraffic: sub.remainingTraffic,
      totalTraffic: sub.totalTraffic,
      usedTraffic: sub.usedTraffic,
      expiryDate: sub.expiryDate,
      status: sub.status
    }));
    
    const response: ApiResponse<typeof userSubscriptions> = {
      success: true,
      data: userSubscriptions
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取订阅列表失败'
    };
    res.status(500).json(response);
  }
});

// 获取所有订阅（管理员后台）
app.get('/api/admin/subscriptions', async (_req, res) => {
  try {
    const subscriptions = await db.getAllSubscriptions();
    const response: ApiResponse<typeof subscriptions> = {
      success: true,
      data: subscriptions
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取订阅列表失败'
    };
    res.status(500).json(response);
  }
});

// 获取单个订阅
app.get('/api/admin/subscriptions/:id', async (req, res) => {
  try {
    const subscription = await db.getSubscriptionById(req.params.id);
    if (!subscription) {
      const response: ApiResponse<null> = {
        success: false,
        message: '订阅不存在'
      };
      return res.status(404).json(response);
    }
    
    const response: ApiResponse<typeof subscription> = {
      success: true,
      data: subscription
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取订阅失败'
    };
    res.status(500).json(response);
  }
});

// 创建订阅
app.post('/api/admin/subscriptions', async (req, res) => {
  try {
    const subscription = await db.createSubscription(req.body);
    const response: ApiResponse<typeof subscription> = {
      success: true,
      data: subscription,
      message: '订阅创建成功'
    };
    res.status(201).json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '创建订阅失败'
    };
    res.status(500).json(response);
  }
});

// 更新订阅
app.put('/api/admin/subscriptions/:id', async (req, res) => {
  try {
    const subscription = await db.updateSubscription(req.params.id, req.body);
    if (!subscription) {
      const response: ApiResponse<null> = {
        success: false,
        message: '订阅不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<typeof subscription> = {
      success: true,
      data: subscription,
      message: '订阅更新成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '更新订阅失败'
    };
    res.status(500).json(response);
  }
});

// 刷新订阅信息（重新解析）
app.post('/api/admin/subscriptions/:id/refresh', async (req, res) => {
  try {
    const subscription = await db.getSubscriptionById(req.params.id);
    if (!subscription) {
      const response: ApiResponse<null> = {
        success: false,
        message: '订阅不存在'
      };
      return res.status(404).json(response);
    }

    console.log('刷新订阅信息:', subscription.name, subscription.url);

    try {
      // 重新解析订阅信息
      const parsedInfo = await parser.parseSubscription(subscription.url);

      // 更新订阅信息，但保留原有的名称和到期时间（除非解析到了新的）
      const updateData = {
        totalTraffic: parsedInfo.totalTraffic || subscription.totalTraffic,
        usedTraffic: parsedInfo.usedTraffic || 0,
        status: parsedInfo.status || 'active',
        // 只有解析到新的到期时间才更新
        ...(parsedInfo.expiryDate && { expiryDate: parsedInfo.expiryDate })
      };

      const updatedSubscription = await db.updateSubscription(req.params.id, updateData);

      const response: ApiResponse<typeof updatedSubscription> = {
        success: true,
        data: updatedSubscription,
        message: '订阅信息刷新成功'
      };
      res.json(response);
    } catch (parseError) {
      console.error('解析订阅失败:', parseError);

      // 解析失败时，将状态设置为错误状态
      const updateData = {
        status: 'error' as const
      };

      const updatedSubscription = await db.updateSubscription(req.params.id, updateData);

      const response: ApiResponse<typeof updatedSubscription> = {
        success: true,
        data: updatedSubscription,
        message: '无法获取订阅信息，已标记为异常状态'
      };
      res.json(response);
    }
  } catch (error) {
    console.error('刷新订阅失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '刷新订阅失败'
    };
    res.status(500).json(response);
  }
});

// 删除订阅
app.delete('/api/admin/subscriptions/:id', async (req, res) => {
  try {
    const deleted = await db.deleteSubscription(req.params.id);
    if (!deleted) {
      const response: ApiResponse<null> = {
        success: false,
        message: '订阅不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<null> = {
      success: true,
      message: '订阅删除成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '删除订阅失败'
    };
    res.status(500).json(response);
  }
});

// 获取流量记录
app.get('/api/admin/traffic-records', async (req, res) => {
  try {
    const { subscriptionId, hours } = req.query;
    const records = await db.getTrafficRecords(
      subscriptionId as string,
      hours ? parseInt(hours as string) : undefined
    );

    const response: ApiResponse<typeof records> = {
      success: true,
      data: records,
      message: '获取流量记录成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取流量记录失败'
    };
    res.status(500).json(response);
  }
});

// 手动记录当前流量（旧方法，只记录数据库中的数据）
app.post('/api/admin/record-traffic', async (_req, res) => {
  try {
    await db.recordCurrentTraffic();

    const response: ApiResponse<null> = {
      success: true,
      message: '流量记录成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '记录流量失败'
    };
    res.status(500).json(response);
  }
});

// 智能记录流量（新方法，先刷新订阅再记录）
app.post('/api/admin/smart-record-traffic', async (_req, res) => {
  try {
    const result = await db.recordTrafficWithRefresh(parser);

    const response: ApiResponse<{ successCount: number; failureCount: number }> = {
      success: true,
      data: result,
      message: `智能流量记录完成：成功 ${result.successCount} 个，失败 ${result.failureCount} 个`
    };
    res.json(response);
  } catch (error) {
    console.error('智能流量记录失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '智能流量记录失败'
    };
    res.status(500).json(response);
  }
});

// 清理不完整的流量记录
app.post('/api/admin/cleanup-records', async (_req, res) => {
  try {
    await db.cleanupIncompleteRecords();

    const response: ApiResponse<null> = {
      success: true,
      message: '数据清理完成'
    };
    res.json(response);
  } catch (error) {
    console.error('数据清理失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '数据清理失败'
    };
    res.status(500).json(response);
  }
});

// 批量刷新所有订阅
app.post('/api/admin/refresh-all-subscriptions', async (_req, res) => {
  try {
    const subscriptions = await db.getAllSubscriptions();
    let successCount = 0;
    let failureCount = 0;
    const results: { id: string; name: string; success: boolean; error?: string }[] = [];

    console.log(`开始批量刷新 ${subscriptions.length} 个订阅...`);
    const startTime = Date.now();
    const overallTimeout = 3 * 60 * 1000; // 3分钟整体超时

    // 并发刷新所有订阅，但限制并发数量避免过载
    const batchSize = 3; // 减少并发数
    for (let i = 0; i < subscriptions.length; i += batchSize) {
      // 检查整体超时
      if (Date.now() - startTime > overallTimeout) {
        console.warn(`批量刷新超时，已处理 ${i} 个订阅，剩余 ${subscriptions.length - i} 个跳过`);
        // 为剩余订阅添加跳过记录
        for (let j = i; j < subscriptions.length; j++) {
          results.push({
            id: subscriptions[j].id,
            name: subscriptions[j].name,
            success: false,
            error: '整体操作超时，跳过处理'
          });
          failureCount++;
        }
        break;
      }

      const batch = subscriptions.slice(i, i + batchSize);

      await Promise.all(batch.map(async (subscription) => {
        const subscriptionTimeout = 10000; // 10秒单个订阅超时

        try {
          console.log(`刷新订阅: ${subscription.name}`);

          // 使用Promise.race实现超时控制
          const parsePromise = parser.parseSubscription(subscription.url);
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('订阅解析超时')), subscriptionTimeout);
          });

          const parsedInfo = await Promise.race([parsePromise, timeoutPromise]) as any;

          // 更新订阅信息
          const updateData = {
            totalTraffic: parsedInfo.totalTraffic || subscription.totalTraffic,
            usedTraffic: parsedInfo.usedTraffic || 0,
            status: parsedInfo.status || 'active',
            ...(parsedInfo.expiryDate && { expiryDate: parsedInfo.expiryDate })
          };

          await db.updateSubscription(subscription.id, updateData);

          results.push({
            id: subscription.id,
            name: subscription.name,
            success: true
          });
          successCount++;
          console.log(`✓ 刷新订阅 ${subscription.name} 成功`);
        } catch (error) {
          console.error(`✗ 刷新订阅 ${subscription.name} 失败:`, (error as any).message || error);

          // 解析失败时，将状态设置为错误状态
          try {
            await db.updateSubscription(subscription.id, { status: 'error' as const });
          } catch (updateError) {
            console.error(`更新订阅 ${subscription.name} 状态失败:`, updateError);
          }

          results.push({
            id: subscription.id,
            name: subscription.name,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          });
          failureCount++;
        }
      }));

      // 批次间短暂延迟
      if (i + batchSize < subscriptions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log(`批量刷新完成：成功 ${successCount} 个，失败 ${failureCount} 个，耗时 ${duration} 秒`);

    const response: ApiResponse<{ successCount: number; failureCount: number; results: typeof results }> = {
      success: true,
      data: { successCount, failureCount, results },
      message: `批量刷新完成：成功 ${successCount} 个，失败 ${failureCount} 个，耗时 ${duration} 秒`
    };
    res.json(response);
  } catch (error) {
    console.error('批量刷新订阅失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '批量刷新订阅失败'
    };
    res.status(500).json(response);
  }
});

// 获取系统配置
app.get('/api/admin/system-config', async (_req, res) => {
  try {
    const systemConfig = await db.getSystemConfig();
    const response: ApiResponse<typeof systemConfig> = {
      success: true,
      data: systemConfig,
      message: '获取系统配置成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取系统配置失败'
    };
    res.status(500).json(response);
  }
});

// 更新系统配置
app.put('/api/admin/system-config', async (req, res) => {
  try {
    const { recordInterval } = req.body;

    if (!recordInterval || recordInterval < 1 || recordInterval > 1440) {
      const response: ApiResponse<null> = {
        success: false,
        message: '记录间隔必须在1-1440分钟之间'
      };
      return res.status(400).json(response);
    }

    await db.updateSystemConfig({ recordInterval });

    // 立即更新定时任务，不等待下次检查
    if (updateRecordingInterval) {
      try {
        await updateRecordingInterval();
        console.log(`配置更新后立即应用新的记录间隔：${recordInterval}分钟`);
      } catch (updateError) {
        console.error('立即更新定时任务失败:', updateError);
      }
    }

    const response: ApiResponse<null> = {
      success: true,
      message: '系统配置更新成功，新的记录间隔已立即生效'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '更新系统配置失败'
    };
    res.status(500).json(response);
  }
});

// 修改管理员密码
app.post('/api/admin/change-password', async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      const response: ApiResponse<null> = {
        success: false,
        message: '请提供当前密码和新密码'
      };
      return res.status(400).json(response);
    }

    // 验证当前密码
    if (currentPassword !== config.adminPassword) {
      const response: ApiResponse<null> = {
        success: false,
        message: '当前密码错误'
      };
      return res.status(401).json(response);
    }

    if (newPassword.length < 6) {
      const response: ApiResponse<null> = {
        success: false,
        message: '新密码长度至少6位'
      };
      return res.status(400).json(response);
    }

    // 注意：这里只是演示，实际生产环境中应该将密码保存到环境变量或配置文件
    // 由于这是演示项目，我们只在内存中更新密码
    (config as any).adminPassword = newPassword;

    const response: ApiResponse<null> = {
      success: true,
      message: '密码修改成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '密码修改失败'
    };
    res.status(500).json(response);
  }
});

// 手动更新智能记录间隔
app.post('/api/admin/update-recording-interval', async (_req, res) => {
  try {
    if (updateRecordingInterval) {
      await updateRecordingInterval();
      const systemConfig = await db.getSystemConfig();
      const response: ApiResponse<{ currentInterval: number }> = {
        success: true,
        data: { currentInterval: systemConfig.recordInterval },
        message: `智能记录间隔已更新为 ${systemConfig.recordInterval} 分钟`
      };
      res.json(response);
    } else {
      const response: ApiResponse<null> = {
        success: false,
        message: '定时任务未初始化'
      };
      res.status(500).json(response);
    }
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '更新智能记录间隔失败'
    };
    res.status(500).json(response);
  }
});

// 获取机场列表
app.get('/api/admin/airports', async (_req, res) => {
  try {
    const airports = await db.getAllAirports();
    const response: ApiResponse<typeof airports> = {
      success: true,
      data: airports,
      message: '获取机场列表成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取机场列表失败'
    };
    res.status(500).json(response);
  }
});

// 创建机场信息
app.post('/api/admin/airports', async (req, res) => {
  try {
    const { name, domain, website, description } = req.body;

    if (!name || !domain) {
      const response: ApiResponse<null> = {
        success: false,
        message: '机场名称和域名为必填项'
      };
      return res.status(400).json(response);
    }

    const airport = await db.createAirport({ name, domain, website, description });

    const response: ApiResponse<typeof airport> = {
      success: true,
      data: airport,
      message: '机场信息创建成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '创建机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 更新机场信息
app.put('/api/admin/airports/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, domain, website, description } = req.body;

    const airport = await db.updateAirport(id, { name, domain, website, description });

    const response: ApiResponse<typeof airport> = {
      success: true,
      data: airport,
      message: '机场信息更新成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '更新机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 删除机场信息
app.delete('/api/admin/airports/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await db.deleteAirport(id);

    const response: ApiResponse<null> = {
      success: true,
      message: '机场信息删除成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '删除机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 从订阅中提取机场信息
app.post('/api/admin/extract-airports', async (_req, res) => {
  try {
    const result = await db.extractAirportsFromSubscriptions();

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: `机场信息提取完成：新增 ${result.newCount} 个，更新 ${result.updatedCount} 个`
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '提取机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 获取节点池配置
app.get('/api/admin/nodepool/config', async (_req, res) => {
  try {
    const config = await db.getNodePoolConfig();
    const response: ApiResponse<typeof config> = {
      success: true,
      data: config,
      message: '获取节点池配置成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取节点池配置失败'
    };
    res.status(500).json(response);
  }
});

// 更新节点池配置
app.put('/api/admin/nodepool/config', async (req, res) => {
  try {
    console.log('更新节点池配置:', req.body);
    await db.updateNodePoolConfig(req.body);

    const response: ApiResponse<null> = {
      success: true,
      message: '节点池配置更新成功'
    };
    res.json(response);
  } catch (error) {
    console.error('更新节点池配置失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '更新节点池配置失败'
    };
    res.status(500).json(response);
  }
});

// 获取节点池原始订阅内容（用于订阅转换后端）
app.get('/api/nodepool/raw', async (req, res) => {
  try {
    const { token } = req.query;
    const config = await db.getNodePoolConfig();

    // 验证token
    const validTokens = [config.token, config.guestToken].filter(t => t && t.trim() !== '');
    if (!token || !validTokens.includes(token as string)) {
      return res.status(401).send('Unauthorized');
    }

    const nodePoolService = new NodePoolService(config);

    // 获取base64格式的原始订阅
    const content = await nodePoolService.generateBase64Subscription();

    // 计算订阅信息
    const subscriptionInfo = nodePoolService.calculateSubscriptionInfo();

    res.set({
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'no-cache',
      'Profile-Update-Interval': `${config.updateInterval}`,
      'Subscription-Userinfo': `upload=${subscriptionInfo.upload}; download=${subscriptionInfo.download}; total=${subscriptionInfo.total}; expire=${subscriptionInfo.expire}`
    });

    // 确保不设置任何会导致下载的响应头
    res.removeHeader('Content-Disposition');

    res.send(content);
  } catch (error) {
    console.error('生成节点池原始订阅失败:', error);
    res.status(500).send('Internal Server Error');
  }
});

// 获取节点池订阅内容（支持多种格式）
app.get('/api/nodepool/sub', async (req, res) => {
  try {
    const { token, format } = req.query;
    const config = await db.getNodePoolConfig();

    // 验证token
    const validTokens = [config.token, config.guestToken].filter(t => t && t.trim() !== '');
    if (!token || !validTokens.includes(token as string)) {
      return res.status(401).send('Unauthorized');
    }

    const nodePoolService = new NodePoolService(config);

    // 获取请求的基础URL
    const baseUrl = `${req.protocol}://${req.get('host')}`;

    let content: string;
    let contentType: string;

    // 使用订阅转换后端生成不同格式
    content = await nodePoolService.generateSubscription(format as string, baseUrl);

    switch (format) {
      case 'clash':
        contentType = 'text/yaml'; // 改为text/yaml，更容易在浏览器中显示
        break;
      case 'singbox':
        contentType = 'application/json';
        break;
      case 'surge':
        contentType = 'text/plain';
        break;
      case 'quanx':
        contentType = 'text/plain';
        break;
      case 'loon':
        contentType = 'text/plain';
        break;
      case 'base64':
      default:
        contentType = 'text/plain';
        break;
    }

    // 计算订阅信息
    const subscriptionInfo = nodePoolService.calculateSubscriptionInfo();

    res.set({
      'Content-Type': `${contentType}; charset=utf-8`,
      'Cache-Control': 'no-cache',
      'Profile-Update-Interval': `${config.updateInterval}`,
      'Subscription-Userinfo': `upload=${subscriptionInfo.upload}; download=${subscriptionInfo.download}; total=${subscriptionInfo.total}; expire=${subscriptionInfo.expire}`
    });

    // 确保不设置任何会导致下载的响应头
    res.removeHeader('Content-Disposition');

    res.send(content);
  } catch (error) {
    console.error('生成节点池订阅失败:', error);
    res.status(500).send('Internal Server Error');
  }
});

// 测试页面：显示订阅链接和测试按钮
app.get('/nodepool/test', async (req, res) => {
  try {
    const config = await db.getNodePoolConfig();
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const token = config.token || 'auto';

    const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>节点池订阅测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .link-box { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .link { color: blue; text-decoration: underline; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>节点池订阅测试页面</h1>
    <p>点击下面的链接测试订阅内容是否在浏览器中显示：</p>

    <div class="link-box">
        <strong>自适应订阅：</strong><br>
        <a href="${baseUrl}/api/nodepool/sub?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}
        </a>
    </div>

    <div class="link-box">
        <strong>Base64订阅：</strong><br>
        <a href="${baseUrl}/api/nodepool/sub?token=${token}&format=base64" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}&format=base64
        </a>
    </div>

    <div class="link-box">
        <strong>Clash订阅：</strong><br>
        <a href="${baseUrl}/api/nodepool/sub?token=${token}&format=clash" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}&format=clash
        </a>
    </div>

    <div class="link-box">
        <strong>原始订阅：</strong><br>
        <a href="${baseUrl}/api/nodepool/raw?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/raw?token=${token}
        </a>
    </div>

    <div class="result">
        <strong>测试说明：</strong><br>
        1. 点击上面的链接应该在新标签页中显示订阅内容，而不是下载文件<br>
        2. 如果仍然下载文件，请检查浏览器设置或联系管理员<br>
        3. 当前token: ${token}
    </div>
</body>
</html>`;

    res.set('Content-Type', 'text/html; charset=utf-8');
    res.send(testHtml);
  } catch (error) {
    console.error('生成测试页面失败:', error);
    res.status(500).send('Internal Server Error');
  }
});

// 错误处理中间件（必须在所有路由之后）
app.use(notFoundHandler);
app.use(errorHandler);

// 定时任务：根据配置的时间间隔刷新订阅并记录流量数据
let recordingInterval: NodeJS.Timeout | null = null;
let currentIntervalMinutes = 60; // 记录当前使用的间隔

// 导出更新定时任务的函数，供API调用
let updateRecordingInterval: (() => Promise<void>) | null = null;

const startTrafficRecording = async () => {
  const smartRecord = async () => {
    try {
      console.log('开始智能流量记录...');
      const result = await db.recordTrafficWithRefresh(parser);
      console.log(`智能流量记录完成：成功 ${result.successCount} 个，失败 ${result.failureCount} 个`);
    } catch (error) {
      console.error('智能流量记录失败，使用备用方案:', error);
      // 备用方案：只记录当前数据库中的数据
      try {
        await db.recordCurrentTraffic();
        console.log('备用流量记录完成');
      } catch (recordError) {
        console.error('备用流量记录也失败:', recordError);
      }
    }
  };

  const updateRecordingIntervalInternal = async () => {
    try {
      // 清除现有定时器
      if (recordingInterval) {
        clearInterval(recordingInterval);
      }

      // 获取当前配置的时间间隔
      const systemConfig = await db.getSystemConfig();
      const intervalMinutes = systemConfig.recordInterval;
      const intervalMs = intervalMinutes * 60 * 1000;

      // 更新当前间隔记录
      currentIntervalMinutes = intervalMinutes;

      // 设置新的定时器
      recordingInterval = setInterval(smartRecord, intervalMs);

      console.log(`智能流量记录定时任务已更新，每 ${intervalMinutes} 分钟自动刷新订阅并记录最新流量`);
    } catch (error) {
      console.error('更新定时任务失败，使用默认间隔:', error);
      // 使用默认间隔（60分钟）
      if (recordingInterval) {
        clearInterval(recordingInterval);
      }
      currentIntervalMinutes = 60;
      recordingInterval = setInterval(smartRecord, 60 * 60 * 1000);
      console.log('使用默认间隔：每60分钟执行一次智能流量记录');
    }
  };

  // 导出更新函数供API调用
  updateRecordingInterval = updateRecordingIntervalInternal;

  // 立即执行一次记录
  await smartRecord();

  // 设置定时任务
  await updateRecordingIntervalInternal();

  // 每1分钟检查一次配置是否有变化，如有变化则更新定时器
  setInterval(async () => {
    try {
      const currentConfig = await db.getSystemConfig();

      // 检查配置的间隔是否与当前使用的间隔不同
      if (currentConfig.recordInterval !== currentIntervalMinutes) {
        console.log(`检测到配置变化：${currentIntervalMinutes}分钟 -> ${currentConfig.recordInterval}分钟，更新定时任务...`);
        await updateRecordingIntervalInternal();
      }
    } catch (error) {
      console.error('检查配置变化失败:', error);
    }
  }, 1 * 60 * 1000); // 每1分钟检查一次，确保配置变化能快速生效
};

// 启动服务器
app.listen(config.port, () => {
  console.log(`服务器运行在 http://localhost:${config.port}`);
  console.log(`环境: ${config.nodeEnv}`);

  // 启动流量记录定时任务
  startTrafficRecording();
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  db.close();
  process.exit(0);
});
