@echo off
echo 正在安装机场订阅管理系统...
echo.

echo 检查Node.js版本...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检查npm版本...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到npm
    pause
    exit /b 1
)

echo.
echo 1. 设置环境配置...
if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo ✅ 已创建根目录 .env 文件
    )
)

if not exist backend\.env (
    if exist backend\.env.example (
        copy backend\.env.example backend\.env >nul
        echo ✅ 已创建后端 .env 文件
    )
)

echo.
echo 2. 安装根目录依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 根目录依赖安装失败！
    pause
    exit /b 1
)

echo.
echo 3. 安装后端依赖...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败！
    pause
    exit /b 1
)

echo.
echo 4. 安装前端依赖...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败！
    pause
    exit /b 1
)

cd ..
echo.
echo ✅ 所有依赖安装完成！
echo.
echo 📝 配置说明：
echo   - 管理员密码可在 .env 文件中修改 ADMIN_PASSWORD
echo   - 其他配置项请参考 .env.example 文件
echo.
echo 🚀 使用以下命令启动项目：
echo   start.bat            - 启动项目（推荐）
echo   npm run dev          - 同时启动前端和后端
echo   npm run dev:frontend - 只启动前端 (端口 3000)
echo   npm run dev:backend  - 只启动后端 (端口 3001)
echo.
pause
