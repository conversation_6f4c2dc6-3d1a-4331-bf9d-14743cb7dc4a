// 应用配置
export const config = {
  // API 基础地址
  API_BASE_URL: '/api',

  // 应用信息
  APP_NAME: '机场订阅管理系统',
  APP_VERSION: '1.0.0',

  // 本地存储键名
  STORAGE_KEYS: {
    ADMIN_AUTH: 'admin_authenticated'
  },

  // UI配置
  THEME: {
    PRIMARY_COLOR: 'gray',
    LAYOUT: 'minimal'
  },

  // 开发配置
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,

  // 安全配置
  SECURITY: {
    // 注意：生产环境中应该移除前端密码验证，改为后端JWT验证
    ENABLE_FRONTEND_AUTH: import.meta.env.DEV,
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
  }
} as const;
